"""
Gemini LLM provider implementation
"""

import os
import json
from typing import Dict, List, Any, Optional
from google import genai
from google.genai import types

from llm.base import LLMProvider

class GeminiProvider(LLMProvider):
    """Gemini LLM provider using google-genai SDK"""
    
    def _initialize_client(self) -> None:
        """Initialize Gemini client"""
        try:
            # Note that the newest Gemini model series is "gemini-2.5-flash" or gemini-2.5-pro"
            # do not change this unless explicitly requested by the user
            self.client = genai.Client(api_key=self.api_key)
        except Exception as e:
            raise Exception(f"Failed to initialize Gemini client: {e}")
    
    def get_response(self, prompt: str, model: Optional[str] = None, **kwargs) -> str:
        """Get response from Gemini"""
        if not self.client:
            raise Exception("Gemini client not initialized")
        
        model_name = model or self.get_default_model()
        
        try:
            # Handle conversation history if provided
            messages = kwargs.get('messages', [])
            
            if messages:
                # Convert to Gemini format
                contents = []
                system_instruction = None
                
                for msg in messages:
                    role = msg.get('role', '')
                    content = msg.get('content', '')
                    
                    if role == 'system':
                        system_instruction = content
                    elif role in ['user', 'assistant']:
                        gemini_role = 'user' if role == 'user' else 'model'
                        contents.append(types.Content(
                            role=gemini_role,
                            parts=[types.Part(text=content)]
                        ))
                
                # Add current prompt
                contents.append(types.Content(
                    role='user',
                    parts=[types.Part(text=prompt)]
                ))
                
                config = types.GenerateContentConfig()
                if system_instruction:
                    config.system_instruction = system_instruction
                
                response = self.client.models.generate_content(
                    model=model_name,
                    contents=contents,
                    config=config
                )
            else:
                # Simple prompt
                response = self.client.models.generate_content(
                    model=model_name,
                    contents=prompt
                )
            
            return response.text or "No response generated"
            
        except Exception as e:
            raise Exception(f"Gemini API error: {e}")
    
    def get_available_models(self) -> List[str]:
        """Get available Gemini models"""
        return self.config.get('models', [
            'gemini-2.0-flash',
            'gemini-2.5-flash', 
            'gemini-2.5-pro',
            'gemini-1.5-flash',
            'gemini-1.5-pro'
        ])
    
    def test_connection(self) -> bool:
        """Test Gemini API connection"""
        try:
            response = self.client.models.generate_content(
                model=self.get_default_model(),
                contents="Hello, this is a test. Please respond with 'Test successful'."
            )
            return bool(response.text)
        except Exception:
            return False
    
    def analyze_code(self, code: str, language: str = "", model: Optional[str] = None) -> str:
        """Analyze code with Gemini"""
        prompt = f"""Analyze this {language} code and provide:
1. Code quality assessment
2. Potential bugs or issues
3. Performance improvements
4. Best practices suggestions
5. Security considerations

Code:
```{language}
{code}
```"""
        
        return self.get_response(prompt, model)
    
    def suggest_improvements(self, code: str, context: str = "", model: Optional[str] = None) -> str:
        """Suggest code improvements"""
        prompt = f"""Given this code and context, suggest specific improvements:

Context: {context}

Code:
```
{code}
```

Provide specific, actionable suggestions for:
1. Code readability and maintainability
2. Performance optimizations
3. Error handling improvements
4. Best practices adherence
5. Architecture improvements"""
        
        return self.get_response(prompt, model)
