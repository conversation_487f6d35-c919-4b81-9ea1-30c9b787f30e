"""
Terminal and Shell Tools for AI Coding Assistant
"""

import subprocess
import os
import sys
import shlex
import threading
import queue
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass


@dataclass
class CommandResult:
    """Result of command execution"""
    command: str
    exit_code: int
    stdout: str
    stderr: str
    duration: float
    success: bool


class TerminalTools:
    """Advanced terminal and shell operations"""
    
    def __init__(self):
        self.command_history: List[CommandResult] = []
        self.current_directory = Path.cwd()
        self.environment_vars = dict(os.environ)
        self.running_processes: Dict[str, subprocess.Popen] = {}
    
    def run_in_terminal(self, command: str, working_dir: str = None, 
                       timeout: int = 30, shell: bool = True,
                       capture_output: bool = True) -> Dict[str, Any]:
        """Run shell commands with cross-platform support"""
        try:
            start_time = time.time()
            
            # Set working directory
            work_dir = Path(working_dir) if working_dir else self.current_directory
            if not work_dir.exists():
                return {'success': False, 'error': f'Directory not found: {work_dir}'}
            
            # Execute command
            if shell and isinstance(command, str):
                # Shell command
                result = subprocess.run(
                    command,
                    shell=True,
                    capture_output=capture_output,
                    text=True,
                    cwd=str(work_dir),
                    timeout=timeout,
                    env=self.environment_vars
                )
            else:
                # Command with arguments
                if isinstance(command, str):
                    command = shlex.split(command)
                
                result = subprocess.run(
                    command,
                    capture_output=capture_output,
                    text=True,
                    cwd=str(work_dir),
                    timeout=timeout,
                    env=self.environment_vars
                )
            
            duration = time.time() - start_time
            
            # Create result object
            cmd_result = CommandResult(
                command=command if isinstance(command, str) else ' '.join(command),
                exit_code=result.returncode,
                stdout=result.stdout or "",
                stderr=result.stderr or "",
                duration=duration,
                success=result.returncode == 0
            )
            
            # Add to history
            self.command_history.append(cmd_result)
            
            return {
                'success': cmd_result.success,
                'command': cmd_result.command,
                'exit_code': cmd_result.exit_code,
                'stdout': cmd_result.stdout,
                'stderr': cmd_result.stderr,
                'duration': cmd_result.duration,
                'working_directory': str(work_dir)
            }
            
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': f'Command timed out after {timeout} seconds',
                'command': command
            }
        except Exception as e:
            return {'success': False, 'error': str(e), 'command': command}
    
    def get_terminal_output(self, command: str, parse_format: str = "text") -> Dict[str, Any]:
        """Capture and analyze output from command"""
        try:
            result = self.run_in_terminal(command)
            
            if not result['success']:
                return result
            
            output = result['stdout']
            
            # Parse output based on format
            parsed_output = None
            if parse_format == "json":
                try:
                    import json
                    parsed_output = json.loads(output)
                except json.JSONDecodeError:
                    parsed_output = {"raw": output, "parse_error": "Invalid JSON"}
            elif parse_format == "csv":
                parsed_output = self._parse_csv_output(output)
            elif parse_format == "table":
                parsed_output = self._parse_table_output(output)
            else:
                parsed_output = output
            
            return {
                'success': True,
                'command': command,
                'raw_output': output,
                'parsed_output': parsed_output,
                'format': parse_format,
                'lines': output.split('\n') if output else []
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_terminal_last_command(self) -> Dict[str, Any]:
        """Get the last command that was run"""
        if not self.command_history:
            return {'success': False, 'message': 'No commands in history'}
        
        last_cmd = self.command_history[-1]
        return {
            'success': True,
            'command': last_cmd.command,
            'exit_code': last_cmd.exit_code,
            'stdout': last_cmd.stdout,
            'stderr': last_cmd.stderr,
            'duration': last_cmd.duration,
            'success': last_cmd.success
        }
    
    def create_and_run_task(self, task_name: str, command: str, 
                           background: bool = False, auto_restart: bool = False) -> Dict[str, Any]:
        """Define and execute terminal tasks"""
        try:
            if background:
                # Run in background
                process = subprocess.Popen(
                    command,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    cwd=str(self.current_directory),
                    env=self.environment_vars
                )
                
                self.running_processes[task_name] = process
                
                return {
                    'success': True,
                    'task_name': task_name,
                    'command': command,
                    'pid': process.pid,
                    'status': 'running',
                    'background': True
                }
            else:
                # Run synchronously
                result = self.run_in_terminal(command)
                return {
                    'success': result['success'],
                    'task_name': task_name,
                    'command': command,
                    'result': result,
                    'background': False
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_task_output(self, task_name: str) -> Dict[str, Any]:
        """Get log from running build/dev task"""
        try:
            if task_name not in self.running_processes:
                return {'success': False, 'error': f'Task not found: {task_name}'}
            
            process = self.running_processes[task_name]
            
            # Check if process is still running
            if process.poll() is None:
                # Process is running
                status = 'running'
                # Try to read some output without blocking
                try:
                    stdout_data = process.stdout.read(1024) if process.stdout else ""
                    stderr_data = process.stderr.read(1024) if process.stderr else ""
                except:
                    stdout_data = ""
                    stderr_data = ""
            else:
                # Process finished
                status = 'finished'
                stdout_data, stderr_data = process.communicate()
            
            return {
                'success': True,
                'task_name': task_name,
                'status': status,
                'exit_code': process.returncode,
                'stdout': stdout_data,
                'stderr': stderr_data,
                'pid': process.pid
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def install_python_packages(self, packages: List[str], upgrade: bool = False,
                               user_install: bool = False) -> Dict[str, Any]:
        """pip install packages dynamically"""
        try:
            # Build pip command
            cmd_parts = [sys.executable, '-m', 'pip', 'install']
            
            if upgrade:
                cmd_parts.append('--upgrade')
            if user_install:
                cmd_parts.append('--user')
            
            cmd_parts.extend(packages)
            
            # Execute installation
            result = self.run_in_terminal(' '.join(cmd_parts), timeout=120)
            
            if result['success']:
                # Verify installation
                installed = []
                failed = []
                
                for package in packages:
                    verify_result = self.run_in_terminal(f'{sys.executable} -c "import {package.split("==")[0]}"')
                    if verify_result['success']:
                        installed.append(package)
                    else:
                        failed.append(package)
                
                return {
                    'success': len(failed) == 0,
                    'packages_requested': packages,
                    'packages_installed': installed,
                    'packages_failed': failed,
                    'pip_output': result['stdout'],
                    'pip_errors': result['stderr']
                }
            else:
                return {
                    'success': False,
                    'error': 'pip install failed',
                    'packages': packages,
                    'pip_output': result['stdout'],
                    'pip_errors': result['stderr']
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def configure_python_environment(self, env_name: str = None, python_version: str = None,
                                   requirements_file: str = None) -> Dict[str, Any]:
        """Setup and manage venv/conda"""
        try:
            actions_performed = []
            
            # Create virtual environment if specified
            if env_name:
                if python_version:
                    cmd = f'python{python_version} -m venv {env_name}'
                else:
                    cmd = f'python -m venv {env_name}'
                
                result = self.run_in_terminal(cmd)
                if result['success']:
                    actions_performed.append(f'Created virtual environment: {env_name}')
                else:
                    return {
                        'success': False,
                        'error': f'Failed to create virtual environment: {result["stderr"]}'
                    }
            
            # Install requirements if specified
            if requirements_file:
                req_path = Path(requirements_file)
                if req_path.exists():
                    pip_cmd = f'pip install -r {requirements_file}'
                    result = self.run_in_terminal(pip_cmd)
                    if result['success']:
                        actions_performed.append(f'Installed packages from {requirements_file}')
                    else:
                        return {
                            'success': False,
                            'error': f'Failed to install requirements: {result["stderr"]}'
                        }
                else:
                    return {
                        'success': False,
                        'error': f'Requirements file not found: {requirements_file}'
                    }
            
            # Get current Python info
            python_info = self._get_python_info()
            
            return {
                'success': True,
                'actions_performed': actions_performed,
                'python_info': python_info,
                'environment_name': env_name
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_command_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent command history"""
        history = []
        for cmd in self.command_history[-limit:]:
            history.append({
                'command': cmd.command,
                'exit_code': cmd.exit_code,
                'duration': cmd.duration,
                'success': cmd.success,
                'stdout_preview': cmd.stdout[:100] + "..." if len(cmd.stdout) > 100 else cmd.stdout
            })
        return history
    
    def set_working_directory(self, directory: str) -> Dict[str, Any]:
        """Change current working directory"""
        try:
            new_dir = Path(directory)
            if not new_dir.exists():
                return {'success': False, 'error': f'Directory not found: {directory}'}
            
            if not new_dir.is_dir():
                return {'success': False, 'error': f'Not a directory: {directory}'}
            
            old_dir = self.current_directory
            self.current_directory = new_dir
            
            return {
                'success': True,
                'old_directory': str(old_dir),
                'new_directory': str(new_dir),
                'message': f'Changed directory to {new_dir}'
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_environment_info(self) -> Dict[str, Any]:
        """Get current environment information"""
        try:
            python_info = self._get_python_info()
            system_info = self._get_system_info()
            
            return {
                'success': True,
                'current_directory': str(self.current_directory),
                'python_info': python_info,
                'system_info': system_info,
                'environment_variables': dict(self.environment_vars),
                'running_processes': list(self.running_processes.keys())
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _parse_csv_output(self, output: str) -> List[Dict[str, str]]:
        """Parse CSV-like output"""
        lines = output.strip().split('\n')
        if len(lines) < 2:
            return []
        
        headers = [h.strip() for h in lines[0].split(',')]
        rows = []
        
        for line in lines[1:]:
            values = [v.strip() for v in line.split(',')]
            if len(values) == len(headers):
                rows.append(dict(zip(headers, values)))
        
        return rows
    
    def _parse_table_output(self, output: str) -> List[Dict[str, str]]:
        """Parse table-like output (space/tab separated)"""
        lines = output.strip().split('\n')
        if len(lines) < 2:
            return []
        
        # Try to detect column boundaries
        header_line = lines[0]
        headers = header_line.split()
        
        rows = []
        for line in lines[1:]:
            if line.strip():
                values = line.split()
                if len(values) >= len(headers):
                    row_data = dict(zip(headers, values))
                    rows.append(row_data)
        
        return rows
    
    def _get_python_info(self) -> Dict[str, str]:
        """Get Python environment information"""
        return {
            'version': sys.version,
            'executable': sys.executable,
            'platform': sys.platform,
            'path': str(Path(sys.executable).parent)
        }
    
    def _get_system_info(self) -> Dict[str, str]:
        """Get system information"""
        import platform
        
        return {
            'os': platform.system(),
            'version': platform.version(),
            'architecture': platform.architecture()[0],
            'processor': platform.processor(),
            'hostname': platform.node()
        }
    
    def kill_task(self, task_name: str) -> Dict[str, Any]:
        """Kill a running background task"""
        try:
            if task_name not in self.running_processes:
                return {'success': False, 'error': f'Task not found: {task_name}'}
            
            process = self.running_processes[task_name]
            process.terminate()
            
            # Wait a bit for graceful termination
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                # Force kill if needed
                process.kill()
                process.wait()
            
            del self.running_processes[task_name]
            
            return {
                'success': True,
                'task_name': task_name,
                'message': f'Task {task_name} terminated'
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}