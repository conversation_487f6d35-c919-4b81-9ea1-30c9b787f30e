"""
LLM Manager for handling multiple providers with comprehensive tool integration
"""

import json
from typing import Dict, List, Any, Optional
from llm.gemini_provider import GeminiProvider
from llm.openai_provider import OpenAIProvider
from llm.anthropic_provider import AnthropicProvider
from tools.tool_context_manager import ToolContextManager

class LLMManager:
    """Manager for multiple LLM providers with comprehensive tool integration"""
    
    def __init__(self, config):
        self.config = config
        self.providers = {}
        self.tool_manager = ToolContextManager()
        self._initialize_providers()
    
    def _initialize_providers(self) -> None:
        """Initialize available providers"""
        provider_classes = {
            'gemini': GeminiProvider,
            'openai': OpenAIProvider,
            'anthropic': AnthropicProvider
        }
        
        for provider_name, provider_class in provider_classes.items():
            api_key = self.config.get_api_key(provider_name)
            if api_key:
                try:
                    provider_config = self.config.config_data.get('providers', {}).get(provider_name, {})
                    self.providers[provider_name] = provider_class(api_key, provider_config)
                except Exception as e:
                    print(f"Warning: Failed to initialize {provider_name}: {e}")
    
    def get_available_providers(self) -> List[str]:
        """Get list of available providers"""
        return list(self.providers.keys())
    
    def get_provider(self, provider_name: str):
        """Get specific provider instance"""
        if provider_name not in self.providers:
            raise Exception(f"Provider {provider_name} not available or not configured")
        return self.providers[provider_name]
    
    def get_response(self, prompt: str, provider_name: Optional[str] = None, model: Optional[str] = None, 
                    enable_tools: bool = True, **kwargs) -> str:
        """Get response from specified provider with tool capabilities"""
        provider_name = provider_name or self.config.get_default_provider()
        
        if provider_name not in self.providers:
            available = ', '.join(self.get_available_providers())
            raise Exception(f"Provider {provider_name} not available. Available providers: {available}")
        
        # Add tool context to prompt if enabled
        if enable_tools:
            tools_context = self.tool_manager.get_tools_context_for_ai()
            enhanced_prompt = f"{tools_context}\n\nUser Request: {prompt}"
        else:
            enhanced_prompt = prompt
        
        provider = self.providers[provider_name]
        return provider.get_response(enhanced_prompt, model, **kwargs)
    
    def test_provider(self, provider_name: str, model: Optional[str] = None) -> bool:
        """Test specific provider connection"""
        if provider_name not in self.providers:
            return False
        
        try:
            provider = self.providers[provider_name]
            return provider.test_connection()
        except Exception:
            return False
    
    def test_all_providers(self) -> Dict[str, bool]:
        """Test all available providers"""
        results = {}
        for provider_name in self.providers:
            results[provider_name] = self.test_provider(provider_name)
        return results
    
    def get_models(self, provider_name: str) -> List[str]:
        """Get available models for provider"""
        if provider_name not in self.providers:
            return []
        
        provider = self.providers[provider_name]
        return provider.get_available_models()
    
    def get_default_model(self, provider_name: str) -> str:
        """Get default model for provider"""
        if provider_name not in self.providers:
            return ""
        
        provider = self.providers[provider_name]
        return provider.get_default_model()
    
    def analyze_code(self, code: str, language: str = "", provider_name: Optional[str] = None, model: Optional[str] = None) -> str:
        """Analyze code using specified provider"""
        provider_name = provider_name or self.config.get_default_provider()
        
        if provider_name not in self.providers:
            raise Exception(f"Provider {provider_name} not available")
        
        provider = self.providers[provider_name]
        if hasattr(provider, 'analyze_code'):
            return provider.analyze_code(code, language, model)
        else:
            # Fallback to generic response
            prompt = f"Analyze this {language} code and provide feedback:\n\n```{language}\n{code}\n```"
            return provider.get_response(prompt, model)
    
    def suggest_improvements(self, code: str, context: str = "", provider_name: Optional[str] = None, model: Optional[str] = None) -> str:
        """Suggest code improvements using specified provider"""
        provider_name = provider_name or self.config.get_default_provider()
        
        if provider_name not in self.providers:
            raise Exception(f"Provider {provider_name} not available")
        
        provider = self.providers[provider_name]
        if hasattr(provider, 'suggest_improvements'):
            return provider.suggest_improvements(code, context, model)
        else:
            # Fallback to generic response
            prompt = f"Suggest improvements for this code:\n\nContext: {context}\n\nCode:\n```\n{code}\n```"
            return provider.get_response(prompt, model)
    
    def generate_code(self, description: str, language: str = "python", provider_name: Optional[str] = None, model: Optional[str] = None) -> str:
        """Generate code using specified provider"""
        provider_name = provider_name or self.config.get_default_provider()
        
        if provider_name not in self.providers:
            raise Exception(f"Provider {provider_name} not available")
        
        provider = self.providers[provider_name]
        if hasattr(provider, 'generate_code'):
            return provider.generate_code(description, language, model)
        else:
            # Fallback to generic response
            prompt = f"Generate {language} code for: {description}"
            return provider.get_response(prompt, model)
    
    def explain_code(self, code: str, provider_name: Optional[str] = None, model: Optional[str] = None) -> str:
        """Explain code using specified provider"""
        provider_name = provider_name or self.config.get_default_provider()
        
        if provider_name not in self.providers:
            raise Exception(f"Provider {provider_name} not available")
        
        provider = self.providers[provider_name]
        if hasattr(provider, 'explain_code'):
            return provider.explain_code(code, model)
        else:
            # Fallback to generic response
            prompt = f"Explain this code:\n\n```\n{code}\n```"
            return provider.get_response(prompt, model)
    
    def get_provider_info(self, provider_name: str) -> Dict[str, Any]:
        """Get information about a provider"""
        if provider_name not in self.providers:
            return {}
        
        provider = self.providers[provider_name]
        return {
            'name': provider_name,
            'available_models': provider.get_available_models(),
            'default_model': provider.get_default_model(),
            'supports_function_calling': provider.supports_function_calling(),
            'max_tokens': provider.get_max_tokens()
        }
    
    def get_all_provider_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all providers"""
        info = {}
        for provider_name in self.providers:
            info[provider_name] = self.get_provider_info(provider_name)
        return info
    
    # Tool Integration Methods
    
    def execute_tool(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """Execute a specific tool"""
        return self.tool_manager.execute_tool_call(tool_name, **kwargs)
    
    def execute_smart_workflow(self, user_request: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute intelligent workflow with multiple tools"""
        return self.tool_manager.execute_smart_workflow(user_request, context)
    
    def parallel_tool_execution(self, tool_calls: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Execute multiple tools in parallel"""
        return self.tool_manager.parallel_tool_execution(tool_calls)
    
    def get_tool_suggestions(self, current_state: Dict[str, Any], recent_actions: List[str] = None) -> Dict[str, Any]:
        """Get contextual tool suggestions"""
        return self.tool_manager.get_contextual_suggestions(current_state, recent_actions)
    
    def get_available_tools(self) -> List[str]:
        """Get list of all available tools"""
        return self.tool_manager.get_available_tools()
    
    def get_tool_help(self, tool_name: str = None) -> Dict[str, Any]:
        """Get help for tools"""
        return self.tool_manager.get_tool_help(tool_name)
    
    def get_tool_analytics(self) -> Dict[str, Any]:
        """Get tool usage analytics"""
        return self.tool_manager.get_tool_analytics()
    
    def smart_code_assistance(self, user_request: str, provider_name: Optional[str] = None, 
                             model: Optional[str] = None, use_tools: bool = True) -> Dict[str, Any]:
        """Provide intelligent code assistance with tool integration"""
        try:
            if use_tools:
                # Use smart workflow for complex requests
                workflow_result = self.execute_smart_workflow(user_request)
                
                # Get LLM response with tool context
                llm_response = self.get_response(
                    f"Based on the workflow results: {json.dumps(workflow_result, indent=2)}\n\nProvide comprehensive assistance for: {user_request}",
                    provider_name=provider_name,
                    model=model,
                    enable_tools=False  # Avoid recursive tool calling
                )
                
                return {
                    'success': True,
                    'user_request': user_request,
                    'workflow_result': workflow_result,
                    'llm_response': llm_response,
                    'tools_used': workflow_result.get('workflow_steps', [])
                }
            else:
                # Simple LLM response without tools
                response = self.get_response(user_request, provider_name, model, enable_tools=False)
                return {
                    'success': True,
                    'user_request': user_request,
                    'llm_response': response,
                    'tools_used': []
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'user_request': user_request
            }
    
    def adaptive_coding_session(self, initial_request: str, provider_name: Optional[str] = None,
                               model: Optional[str] = None, max_iterations: int = 5) -> Dict[str, Any]:
        """Run adaptive coding session with continuous improvement"""
        try:
            session_results = []
            current_request = initial_request
            
            for iteration in range(max_iterations):
                # Get smart assistance for current request
                assistance_result = self.smart_code_assistance(
                    current_request, 
                    provider_name=provider_name, 
                    model=model
                )
                
                session_results.append({
                    'iteration': iteration + 1,
                    'request': current_request,
                    'result': assistance_result
                })
                
                # Check if we should continue
                if assistance_result.get('success', False):
                    workflow_result = assistance_result.get('workflow_result', {})
                    if workflow_result.get('final_status') == 'completed':
                        break
                
                # Generate improvement request for next iteration
                current_request = f"Improve and fix issues from previous attempt: {current_request}"
            
            return {
                'success': True,
                'initial_request': initial_request,
                'session_results': session_results,
                'total_iterations': len(session_results),
                'provider_used': provider_name or self.config.get_default_provider()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'initial_request': initial_request
            }
