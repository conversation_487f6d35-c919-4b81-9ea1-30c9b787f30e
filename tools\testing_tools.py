"""
Testing and Debugging Tools for AI Coding Assistant
"""

import subprocess
import sys
import os
import re
import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass


@dataclass
class TestResult:
    """Test execution result"""
    test_name: str
    status: str  # 'passed', 'failed', 'error', 'skipped'
    duration: float
    error_message: Optional[str] = None
    traceback: Optional[str] = None


class TestingTools:
    """Advanced testing and debugging utilities"""
    
    def __init__(self):
        self.test_frameworks = {
            'python': ['pytest', 'unittest', 'nose2'],
            'javascript': ['jest', 'mocha', 'jasmine', 'vitest'],
            'java': ['junit', 'testng'],
            'go': ['go test'],
            'rust': ['cargo test']
        }
    
    def test_search(self, source_file: str, test_directory: str = "tests") -> Dict[str, Any]:
        """Find tests related to source files"""
        try:
            source_path = Path(source_file)
            test_dir = Path(test_directory)
            
            if not source_path.exists():
                return {'success': False, 'error': f'Source file not found: {source_file}'}
            
            # Generate possible test file names
            base_name = source_path.stem
            possible_names = [
                f"test_{base_name}.py",
                f"{base_name}_test.py",
                f"test_{base_name}.js",
                f"{base_name}.test.js",
                f"{base_name}.spec.js",
                f"Test{base_name.capitalize()}.java"
            ]
            
            found_tests = []
            
            # Search in test directory
            if test_dir.exists():
                for test_file in test_dir.rglob("*"):
                    if test_file.name in possible_names:
                        found_tests.append({
                            'path': str(test_file),
                            'name': test_file.name,
                            'type': 'direct_match',
                            'confidence': 'high'
                        })
            
            # Search for tests that import or reference the source file
            for test_file in test_dir.rglob("*.py") if test_dir.exists() else []:
                try:
                    with open(test_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check for imports or references
                    if base_name in content:
                        found_tests.append({
                            'path': str(test_file),
                            'name': test_file.name,
                            'type': 'reference_match',
                            'confidence': 'medium'
                        })
                except Exception:
                    continue
            
            return {
                'success': True,
                'source_file': str(source_path),
                'tests_found': len(found_tests),
                'tests': found_tests
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def run_tests(self, test_path: str = ".", framework: str = "auto",
                 verbose: bool = True, pattern: str = None) -> Dict[str, Any]:
        """Execute test suites"""
        try:
            path = Path(test_path)
            if not path.exists():
                return {'success': False, 'error': f'Test path not found: {test_path}'}
            
            # Auto-detect framework if needed
            if framework == "auto":
                framework = self._detect_test_framework(path)
            
            # Build test command
            command = self._build_test_command(framework, str(path), verbose, pattern)
            
            if not command:
                return {'success': False, 'error': f'Unsupported test framework: {framework}'}
            
            # Execute tests
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                cwd=str(path)
            )
            
            # Parse results
            test_results = self._parse_test_output(result.stdout, result.stderr, framework)
            
            return {
                'success': result.returncode == 0,
                'framework': framework,
                'command': ' '.join(command),
                'exit_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'test_results': test_results,
                'summary': self._generate_test_summary(test_results)
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_failure(self, test_output: str, framework: str = "pytest") -> Dict[str, Any]:
        """Capture and analyze test failure messages"""
        try:
            failures = []
            
            if framework == "pytest":
                failures = self._parse_pytest_failures(test_output)
            elif framework == "unittest":
                failures = self._parse_unittest_failures(test_output)
            elif framework == "jest":
                failures = self._parse_jest_failures(test_output)
            
            # Generate suggestions for each failure
            for failure in failures:
                failure['suggestions'] = self._generate_failure_suggestions(failure)
            
            return {
                'success': True,
                'framework': framework,
                'failures_found': len(failures),
                'failures': failures
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def autonomous_debugger(self, file_path: str, error_trace: str = None) -> Dict[str, Any]:
        """Trace, identify and fix bugs automatically"""
        try:
            path = Path(file_path)
            if not path.exists():
                return {'success': False, 'error': f'File not found: {file_path}'}
            
            with open(path, 'r', encoding='utf-8') as f:
                code = f.read()
            
            debug_info = {
                'file_path': str(path),
                'issues_found': [],
                'suggestions': [],
                'potential_fixes': []
            }
            
            # Static analysis for common issues
            lines = code.split('\n')
            for i, line in enumerate(lines, 1):
                # Check for common Python issues
                if path.suffix == '.py':
                    issues = self._analyze_python_line(line, i)
                    debug_info['issues_found'].extend(issues)
                elif path.suffix in ['.js', '.ts']:
                    issues = self._analyze_javascript_line(line, i)
                    debug_info['issues_found'].extend(issues)
            
            # Analyze error trace if provided
            if error_trace:
                trace_analysis = self._analyze_error_trace(error_trace, code)
                debug_info['trace_analysis'] = trace_analysis
                debug_info['suggestions'].extend(trace_analysis.get('suggestions', []))
            
            # Generate potential fixes
            debug_info['potential_fixes'] = self._generate_auto_fixes(debug_info['issues_found'], code)
            
            return {
                'success': True,
                'debug_info': debug_info
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def lint_check(self, file_path: str, linter: str = "auto") -> Dict[str, Any]:
        """Run lint/static analysis on code"""
        try:
            path = Path(file_path)
            if not path.exists():
                return {'success': False, 'error': f'File not found: {file_path}'}
            
            # Auto-detect linter based on file extension
            if linter == "auto":
                linter = self._detect_linter(path)
            
            command = self._build_lint_command(linter, str(path))
            
            if not command:
                return {'success': False, 'error': f'No suitable linter found for {path.suffix}'}
            
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                cwd=str(path.parent)
            )
            
            # Parse lint output
            issues = self._parse_lint_output(result.stdout, result.stderr, linter)
            
            return {
                'success': True,
                'linter': linter,
                'command': ' '.join(command),
                'exit_code': result.returncode,
                'issues_found': len(issues),
                'issues': issues,
                'summary': self._categorize_lint_issues(issues)
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _detect_test_framework(self, path: Path) -> str:
        """Auto-detect testing framework"""
        # Check for config files
        if (path / "pytest.ini").exists() or (path / "pyproject.toml").exists():
            return "pytest"
        elif (path / "jest.config.js").exists() or (path / "package.json").exists():
            return "jest"
        elif (path / "pom.xml").exists():
            return "junit"
        elif (path / "Cargo.toml").exists():
            return "cargo test"
        
        # Check file patterns in test directory
        test_files = list(path.rglob("test_*.py")) + list(path.rglob("*_test.py"))
        if test_files:
            return "pytest"
        
        js_test_files = list(path.rglob("*.test.js")) + list(path.rglob("*.spec.js"))
        if js_test_files:
            return "jest"
        
        return "pytest"  # Default
    
    def _build_test_command(self, framework: str, path: str, verbose: bool, pattern: str) -> List[str]:
        """Build test execution command"""
        if framework == "pytest":
            cmd = ["python", "-m", "pytest", path]
            if verbose:
                cmd.append("-v")
            if pattern:
                cmd.extend(["-k", pattern])
            return cmd
        elif framework == "jest":
            cmd = ["npx", "jest", path]
            if verbose:
                cmd.append("--verbose")
            if pattern:
                cmd.extend(["--testNamePattern", pattern])
            return cmd
        elif framework == "unittest":
            cmd = ["python", "-m", "unittest"]
            if verbose:
                cmd.append("-v")
            cmd.append("discover")
            cmd.extend(["-s", path])
            return cmd
        
        return []
    
    def _parse_test_output(self, stdout: str, stderr: str, framework: str) -> List[TestResult]:
        """Parse test execution output"""
        results = []
        
        if framework == "pytest":
            # Parse pytest output
            test_pattern = r"(\S+)::\S+ (PASSED|FAILED|ERROR|SKIPPED)"
            for match in re.finditer(test_pattern, stdout):
                results.append(TestResult(
                    test_name=match.group(1),
                    status=match.group(2).lower(),
                    duration=0.0  # Could extract from detailed output
                ))
        
        return results
    
    def _generate_test_summary(self, results: List[TestResult]) -> Dict[str, int]:
        """Generate test execution summary"""
        summary = {'passed': 0, 'failed': 0, 'error': 0, 'skipped': 0}
        for result in results:
            summary[result.status] = summary.get(result.status, 0) + 1
        return summary
    
    def _parse_pytest_failures(self, output: str) -> List[Dict[str, Any]]:
        """Parse pytest failure output"""
        failures = []
        # Implementation would parse pytest-specific failure format
        return failures
    
    def _parse_unittest_failures(self, output: str) -> List[Dict[str, Any]]:
        """Parse unittest failure output"""
        failures = []
        # Implementation would parse unittest-specific failure format
        return failures
    
    def _parse_jest_failures(self, output: str) -> List[Dict[str, Any]]:
        """Parse jest failure output"""
        failures = []
        # Implementation would parse jest-specific failure format
        return failures
    
    def _generate_failure_suggestions(self, failure: Dict[str, Any]) -> List[str]:
        """Generate suggestions for test failures"""
        suggestions = []
        error_msg = failure.get('error_message', '').lower()
        
        if 'assertion' in error_msg:
            suggestions.append("Check if the expected and actual values are correct")
        if 'import' in error_msg:
            suggestions.append("Verify that all required modules are imported")
        if 'attribute' in error_msg:
            suggestions.append("Check if the object has the required attribute or method")
        
        return suggestions
    
    def _analyze_python_line(self, line: str, line_num: int) -> List[Dict[str, Any]]:
        """Analyze a Python line for potential issues"""
        issues = []
        stripped = line.strip()
        
        if stripped.startswith('except:'):
            issues.append({
                'line': line_num,
                'type': 'bare_except',
                'severity': 'warning',
                'message': 'Bare except clause catches all exceptions',
                'suggestion': 'Specify the exception type'
            })
        
        if 'print(' in stripped and not stripped.strip().startswith('#'):
            issues.append({
                'line': line_num,
                'type': 'debug_print',
                'severity': 'info',
                'message': 'Debug print statement found',
                'suggestion': 'Consider using logging instead of print'
            })
        
        return issues
    
    def _analyze_javascript_line(self, line: str, line_num: int) -> List[Dict[str, Any]]:
        """Analyze a JavaScript line for potential issues"""
        issues = []
        stripped = line.strip()
        
        if 'console.log(' in stripped:
            issues.append({
                'line': line_num,
                'type': 'debug_console',
                'severity': 'info',
                'message': 'Debug console.log found',
                'suggestion': 'Remove debug console.log statements'
            })
        
        if re.search(r'\bvar\s+', stripped):
            issues.append({
                'line': line_num,
                'type': 'var_usage',
                'severity': 'warning',
                'message': 'Use of var keyword',
                'suggestion': 'Use let or const instead of var'
            })
        
        return issues
    
    def _analyze_error_trace(self, trace: str, code: str) -> Dict[str, Any]:
        """Analyze error traceback"""
        analysis = {
            'error_type': 'unknown',
            'line_number': None,
            'suggestions': []
        }
        
        # Parse common error patterns
        if 'NameError' in trace:
            analysis['error_type'] = 'NameError'
            analysis['suggestions'].append('Check if the variable is defined before use')
        elif 'AttributeError' in trace:
            analysis['error_type'] = 'AttributeError' 
            analysis['suggestions'].append('Verify the object has the requested attribute')
        elif 'TypeError' in trace:
            analysis['error_type'] = 'TypeError'
            analysis['suggestions'].append('Check if the correct types are being used')
        
        # Extract line number
        line_match = re.search(r'line (\d+)', trace)
        if line_match:
            analysis['line_number'] = int(line_match.group(1))
        
        return analysis
    
    def _generate_auto_fixes(self, issues: List[Dict[str, Any]], code: str) -> List[Dict[str, Any]]:
        """Generate automatic fixes for common issues"""
        fixes = []
        
        for issue in issues:
            if issue['type'] == 'bare_except':
                fixes.append({
                    'line': issue['line'],
                    'fix_type': 'replace',
                    'original': 'except:',
                    'replacement': 'except Exception as e:',
                    'description': 'Replace bare except with specific exception handling'
                })
            elif issue['type'] == 'debug_print':
                fixes.append({
                    'line': issue['line'],
                    'fix_type': 'replace',
                    'original': 'print(',
                    'replacement': 'logging.info(',
                    'description': 'Replace print with logging'
                })
        
        return fixes
    
    def _detect_linter(self, path: Path) -> str:
        """Detect appropriate linter for file"""
        if path.suffix == '.py':
            return 'flake8'
        elif path.suffix in ['.js', '.jsx']:
            return 'eslint'
        elif path.suffix in ['.ts', '.tsx']:
            return 'tslint'
        return 'none'
    
    def _build_lint_command(self, linter: str, file_path: str) -> List[str]:
        """Build linting command"""
        if linter == 'flake8':
            return ['python', '-m', 'flake8', file_path]
        elif linter == 'eslint':
            return ['npx', 'eslint', file_path]
        elif linter == 'tslint':
            return ['npx', 'tslint', file_path]
        return []
    
    def _parse_lint_output(self, stdout: str, stderr: str, linter: str) -> List[Dict[str, Any]]:
        """Parse linter output"""
        issues = []
        
        if linter == 'flake8':
            # Parse flake8 format: filename:line:col: code message
            pattern = r'([^:]+):(\d+):(\d+): (\w+) (.+)'
            for match in re.finditer(pattern, stdout):
                issues.append({
                    'file': match.group(1),
                    'line': int(match.group(2)),
                    'column': int(match.group(3)),
                    'code': match.group(4),
                    'message': match.group(5),
                    'severity': 'warning'
                })
        
        return issues
    
    def _categorize_lint_issues(self, issues: List[Dict[str, Any]]) -> Dict[str, int]:
        """Categorize lint issues by severity"""
        categories = {'error': 0, 'warning': 0, 'info': 0}
        for issue in issues:
            severity = issue.get('severity', 'warning')
            categories[severity] = categories.get(severity, 0) + 1
        return categories