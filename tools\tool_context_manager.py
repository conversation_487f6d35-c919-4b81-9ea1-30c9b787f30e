"""
Tool Context Manager - Provides AI with comprehensive tool calling capabilities
"""

import json
import inspect
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass
from tools.smart_orchestrator import SmartOrchestrator


@dataclass
class ToolDefinition:
    """Definition of a tool for AI context"""
    name: str
    description: str
    parameters: Dict[str, Any]
    category: str
    usage_examples: List[str]
    returns: str


class ToolContextManager:
    """Manages tool context and provides AI with tool calling capabilities"""
    
    def __init__(self):
        self.orchestrator = SmartOrchestrator()
        self.tool_definitions = self._generate_tool_definitions()
        self.tool_usage_context = ""
        
    def get_tools_context_for_ai(self) -> str:
        """Generate comprehensive tool context for AI"""
        context = """# AI Coding Assistant - Comprehensive Tool Suite

You have access to a powerful suite of tools organized into the following categories:

## 🛠️ File System Tools
- **create_file**: Create files with content
- **edit_file**: Insert, delete, modify lines or sections with multiple operations
- **create_directory**: Create nested folders/directories  
- **read_file**: Read specific lines or full content
- **file_search**: Search files by glob patterns
- **grep_search**: Regex/text search inside files
- **list_dir**: List files/folders in directory with filtering
- **semantic_search**: Natural language search across codebase

## 🧪 Testing & Debugging Tools
- **test_search**: Find tests related to source files
- **run_tests**: Execute test suites with framework auto-detection
- **test_failure**: Capture and analyze test failure messages
- **autonomous_debugger**: Trace, identify and fix bugs automatically
- **lint_check**: Run lint/static analysis on code
- **analyze_file**: Comprehensive code analysis and metrics
- **analyze_project**: Full project structure analysis

## 💬 Terminal & Shell Tools
- **run_in_terminal**: Execute shell commands (cross-platform)
- **get_terminal_output**: Capture and parse command output
- **get_terminal_last_command**: Get last executed command details
- **create_and_run_task**: Define and execute background tasks
- **get_task_output**: Monitor running task logs
- **install_python_packages**: Dynamic pip package installation
- **configure_python_environment**: Setup venv/conda environments

## 🌐 Web & Search Tools
- **fetch_webpage**: Scrape webpage content with text extraction
- **semantic_web_search**: Natural language web search
- **github_repo_search**: Search GitHub repositories
- **retrieval_augmented_generation**: Extract content from multiple sources

## 🧠 AI Reasoning & Smart Tools
- **natural_language_to_code**: Convert descriptions to code
- **intent_recognition**: Understand user goals and context
- **chain_of_thought_reasoning**: Break complex problems into steps
- **multi_step_loop**: Code → Run → Fix → Test → Refactor automation
- **context_tracking_memory**: Track conversation and actions
- **predict_next_code_block**: Anticipate next code segments
- **smart_status_report**: Intelligent progress reporting
- **auto_tool_invocation**: Automatically select and run tools

## 🔄 Smart Orchestration Features
- **execute_smart_workflow**: End-to-end intelligent task execution
- **parallel_tool_execution**: Run multiple tools simultaneously
- **contextual_tool_suggestion**: Recommend next tools based on context
- **adaptive_tool_pipeline**: Self-adjusting multi-step workflows

## Tool Usage Guidelines

1. **Smart Orchestration**: Use `execute_smart_workflow` for complex requests that need multiple steps
2. **Parallel Execution**: When possible, run independent tools simultaneously using `parallel_tool_execution`
3. **Context Awareness**: Always consider previous actions and current state when selecting tools
4. **Error Handling**: If tools fail, use debugging tools (`autonomous_debugger`, `lint_check`) to diagnose
5. **Progressive Enhancement**: Start simple, then add complexity based on results

## Example Usage Patterns

### File Operations
```python
# Create and analyze a new file
tools = [
    {'tool': 'create_file', 'args': {'file_path': 'example.py', 'content': code}},
    {'tool': 'analyze_file', 'args': {'file_path': 'example.py'}},
    {'tool': 'lint_check', 'args': {'file_path': 'example.py'}}
]
result = parallel_tool_execution(tools)
```

### Development Workflow  
```python
# Complete development cycle
workflow_result = execute_smart_workflow("Create a Python API server with tests")
```

### Debugging Session
```python
# Automated debugging
debug_tools = [
    {'tool': 'autonomous_debugger', 'args': {'file_path': 'buggy.py'}},
    {'tool': 'test_search', 'args': {'source_file': 'buggy.py'}},
    {'tool': 'run_tests', 'args': {'test_path': 'tests/'}}
]
```

You can invoke tools individually or use the smart orchestration features for complex workflows. Always choose the most appropriate tool combination for the user's request.
"""
        return context
    
    def execute_tool_call(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """Execute a specific tool call"""
        try:
            if hasattr(self.orchestrator, 'tool_methods') and tool_name in self.orchestrator.tool_methods:
                method = self.orchestrator.tool_methods[tool_name]
                result = method(**kwargs)
                
                # Track tool usage
                self._track_tool_usage(tool_name, kwargs, result)
                
                return {
                    'success': True,
                    'tool_name': tool_name,
                    'result': result
                }
            else:
                return {
                    'success': False,
                    'error': f'Tool not found: {tool_name}',
                    'available_tools': list(self.orchestrator.tool_methods.keys())
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'tool_name': tool_name
            }
    
    def execute_smart_workflow(self, user_request: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute intelligent workflow"""
        return self.orchestrator.execute_smart_workflow(user_request, context)
    
    def parallel_tool_execution(self, tool_calls: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Execute multiple tools in parallel"""
        return self.orchestrator.parallel_tool_execution(tool_calls)
    
    def get_contextual_suggestions(self, current_state: Dict[str, Any], 
                                  recent_actions: List[str] = None) -> Dict[str, Any]:
        """Get contextual tool suggestions"""
        return self.orchestrator.contextual_tool_suggestion(current_state, recent_actions)
    
    def get_tool_analytics(self) -> Dict[str, Any]:
        """Get tool usage analytics"""
        return self.orchestrator.get_tool_analytics()
    
    def get_available_tools(self) -> List[str]:
        """Get list of all available tools"""
        return list(self.orchestrator.tool_methods.keys())
    
    def get_tool_help(self, tool_name: str = None) -> Dict[str, Any]:
        """Get help for specific tool or all tools"""
        try:
            if tool_name:
                if tool_name in self.tool_definitions:
                    definition = self.tool_definitions[tool_name]
                    return {
                        'success': True,
                        'tool_name': tool_name,
                        'definition': definition.__dict__
                    }
                else:
                    return {
                        'success': False,
                        'error': f'Tool not found: {tool_name}'
                    }
            else:
                # Return all tool definitions
                all_definitions = {}
                for name, definition in self.tool_definitions.items():
                    all_definitions[name] = {
                        'description': definition.description,
                        'category': definition.category,
                        'parameters': definition.parameters
                    }
                
                return {
                    'success': True,
                    'total_tools': len(all_definitions),
                    'tools': all_definitions
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_tool_definitions(self) -> Dict[str, ToolDefinition]:
        """Generate comprehensive tool definitions"""
        definitions = {}
        
        # File System Tools
        definitions['create_file'] = ToolDefinition(
            name='create_file',
            description='Create a new file with specified content',
            parameters={
                'file_path': {'type': 'string', 'required': True, 'description': 'Path for the new file'},
                'content': {'type': 'string', 'required': False, 'description': 'File content', 'default': ''},
                'encoding': {'type': 'string', 'required': False, 'description': 'File encoding', 'default': 'utf-8'}
            },
            category='filesystem',
            usage_examples=[
                "create_file('app.py', 'print(\"Hello World\")')",
                "create_file('config.json', json.dumps(config_data))"
            ],
            returns='Dict with success status, file path, and metadata'
        )
        
        definitions['edit_file'] = ToolDefinition(
            name='edit_file',
            description='Edit existing file with multiple operations',
            parameters={
                'file_path': {'type': 'string', 'required': True, 'description': 'Path to file to edit'},
                'changes': {'type': 'list', 'required': True, 'description': 'List of edit operations (insert, delete, replace)'}
            },
            category='filesystem',
            usage_examples=[
                "edit_file('app.py', [{'operation': 'insert', 'line': 1, 'content': 'import os'}])",
                "edit_file('app.py', [{'operation': 'replace', 'line': 5, 'content': 'new_line'}])"
            ],
            returns='Dict with success status and edit details'
        )
        
        definitions['read_file'] = ToolDefinition(
            name='read_file',
            description='Read file content with optional line range',
            parameters={
                'file_path': {'type': 'string', 'required': True, 'description': 'Path to file to read'},
                'line_start': {'type': 'integer', 'required': False, 'description': 'Start line number'},
                'line_end': {'type': 'integer', 'required': False, 'description': 'End line number'}
            },
            category='filesystem',
            usage_examples=[
                "read_file('app.py')",
                "read_file('app.py', line_start=10, line_end=20)"
            ],
            returns='Dict with file content, lines, and metadata'
        )
        
        # Testing Tools
        definitions['run_tests'] = ToolDefinition(
            name='run_tests',
            description='Execute test suites with framework auto-detection',
            parameters={
                'test_path': {'type': 'string', 'required': False, 'description': 'Path to test directory', 'default': '.'},
                'framework': {'type': 'string', 'required': False, 'description': 'Test framework', 'default': 'auto'},
                'verbose': {'type': 'boolean', 'required': False, 'description': 'Verbose output', 'default': True}
            },
            category='testing',
            usage_examples=[
                "run_tests('tests/')",
                "run_tests(framework='pytest', verbose=True)"
            ],
            returns='Dict with test results, framework used, and summary'
        )
        
        definitions['autonomous_debugger'] = ToolDefinition(
            name='autonomous_debugger',
            description='Automatically trace, identify and fix bugs',
            parameters={
                'file_path': {'type': 'string', 'required': True, 'description': 'Path to file to debug'},
                'error_trace': {'type': 'string', 'required': False, 'description': 'Error traceback if available'}
            },
            category='testing',
            usage_examples=[
                "autonomous_debugger('buggy.py')",
                "autonomous_debugger('app.py', error_trace=traceback_text)"
            ],
            returns='Dict with issues found, suggestions, and potential fixes'
        )
        
        # Terminal Tools
        definitions['run_in_terminal'] = ToolDefinition(
            name='run_in_terminal',
            description='Execute shell commands with cross-platform support',
            parameters={
                'command': {'type': 'string', 'required': True, 'description': 'Command to execute'},
                'working_dir': {'type': 'string', 'required': False, 'description': 'Working directory'},
                'timeout': {'type': 'integer', 'required': False, 'description': 'Timeout in seconds', 'default': 30}
            },
            category='terminal',
            usage_examples=[
                "run_in_terminal('ls -la')",
                "run_in_terminal('python script.py', working_dir='/path/to/project')"
            ],
            returns='Dict with command output, exit code, and execution details'
        )
        
        definitions['install_python_packages'] = ToolDefinition(
            name='install_python_packages',
            description='Install Python packages using pip',
            parameters={
                'packages': {'type': 'list', 'required': True, 'description': 'List of package names to install'},
                'upgrade': {'type': 'boolean', 'required': False, 'description': 'Upgrade existing packages', 'default': False}
            },
            category='terminal',
            usage_examples=[
                "install_python_packages(['requests', 'pandas'])",
                "install_python_packages(['numpy==1.21.0'], upgrade=True)"
            ],
            returns='Dict with installation results and verification'
        )
        
        # Web Tools
        definitions['fetch_webpage'] = ToolDefinition(
            name='fetch_webpage',
            description='Fetch and extract content from web pages',
            parameters={
                'url': {'type': 'string', 'required': True, 'description': 'URL to fetch'},
                'extract_text': {'type': 'boolean', 'required': False, 'description': 'Extract text content', 'default': True}
            },
            category='web',
            usage_examples=[
                "fetch_webpage('https://example.com')",
                "fetch_webpage('https://api.example.com/data', extract_text=False)"
            ],
            returns='Dict with page content, metadata, and extracted text'
        )
        
        definitions['semantic_web_search'] = ToolDefinition(
            name='semantic_web_search',
            description='Natural language web search',
            parameters={
                'query': {'type': 'string', 'required': True, 'description': 'Search query'},
                'max_results': {'type': 'integer', 'required': False, 'description': 'Maximum results', 'default': 10}
            },
            category='web',
            usage_examples=[
                "semantic_web_search('Python async programming best practices')",
                "semantic_web_search('machine learning tutorials', max_results=5)"
            ],
            returns='Dict with search results including titles, URLs, and snippets'
        )
        
        # AI Reasoning Tools
        definitions['natural_language_to_code'] = ToolDefinition(
            name='natural_language_to_code',
            description='Convert natural language descriptions to code',
            parameters={
                'description': {'type': 'string', 'required': True, 'description': 'Natural language description'},
                'language': {'type': 'string', 'required': False, 'description': 'Programming language', 'default': 'python'}
            },
            category='ai_reasoning',
            usage_examples=[
                "natural_language_to_code('Create a function that sorts a list of numbers')",
                "natural_language_to_code('Make a REST API endpoint', language='javascript')"
            ],
            returns='Dict with generated code, intent analysis, and confidence score'
        )
        
        definitions['execute_smart_workflow'] = ToolDefinition(
            name='execute_smart_workflow',
            description='Execute intelligent end-to-end workflows',
            parameters={
                'user_request': {'type': 'string', 'required': True, 'description': 'User request or goal'},
                'context': {'type': 'dict', 'required': False, 'description': 'Additional context'}
            },
            category='orchestration',
            usage_examples=[
                "execute_smart_workflow('Create a web scraper for news articles')",
                "execute_smart_workflow('Set up testing for my Python project')"
            ],
            returns='Dict with workflow steps, results, and final report'
        )
        
        return definitions
    
    def _track_tool_usage(self, tool_name: str, kwargs: Dict[str, Any], result: Dict[str, Any]):
        """Track tool usage for analytics"""
        # This would be expanded to store usage analytics
        pass
    
    def get_tools_for_llm_context(self) -> str:
        """Get formatted tool information for LLM context"""
        return f"""
Available Tools:
{', '.join(self.get_available_tools())}

Use execute_smart_workflow() for complex multi-step tasks.
Use parallel_tool_execution() when you need to run multiple independent tools.
Individual tools can be called directly when you know exactly what's needed.

Key orchestration features:
- Smart workflow execution with automatic tool selection
- Parallel tool execution for efficiency  
- Context-aware tool suggestions
- Adaptive pipelines that adjust based on results
- Comprehensive error handling and debugging

Always consider the user's intent and current context when selecting tools.
"""