"""
Syntax highlighting utilities for AI Coding Assistant
"""

import re
from typing import Dict, List, Any, Optional
from rich.text import Text
from rich.syntax import Syntax
from rich.console import Console

class SyntaxHighlighter:
    """Syntax highlighting for code display in terminal"""
    
    def __init__(self):
        self.console = Console()
        
        # Language mapping for Rich syntax highlighting
        self.language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'jsx',
            '.tsx': 'tsx',
            '.html': 'html',
            '.htm': 'html',
            '.css': 'css',
            '.scss': 'scss',
            '.sass': 'sass',
            '.less': 'less',
            '.json': 'json',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.xml': 'xml',
            '.md': 'markdown',
            '.sql': 'sql',
            '.sh': 'bash',
            '.bash': 'bash',
            '.zsh': 'bash',
            '.fish': 'fish',
            '.ps1': 'powershell',
            '.bat': 'batch',
            '.cmd': 'batch',
            '.java': 'java',
            '.cpp': 'cpp',
            '.cxx': 'cpp',
            '.cc': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.hpp': 'cpp',
            '.cs': 'csharp',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go',
            '.rs': 'rust',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.r': 'r',
            '.m': 'objective-c',
            '.mm': 'objective-c',
            '.vue': 'vue',
            '.svelte': 'svelte',
            '.dart': 'dart',
            '.dockerfile': 'dockerfile',
            '.toml': 'toml',
            '.ini': 'ini',
            '.cfg': 'ini',
            '.conf': 'apache'
        }
        
        # Themes for different contexts
        self.themes = {
            'monokai': 'monokai',
            'github': 'github-dark',
            'vs': 'vs',
            'dracula': 'dracula',
            'one-dark': 'one-dark'
        }
    
    def highlight_code(self, code: str, language: Optional[str] = None, 
                      file_path: Optional[str] = None, theme: str = 'monokai',
                      line_numbers: bool = True) -> Syntax:
        """Highlight code with syntax highlighting"""
        
        # Determine language
        if not language and file_path:
            from pathlib import Path
            ext = Path(file_path).suffix.lower()
            language = self.language_map.get(ext, 'text')
        elif not language:
            language = 'text'
        
        try:
            syntax = Syntax(
                code,
                language,
                theme=theme,
                line_numbers=line_numbers,
                word_wrap=True,
                background_color="default"
            )
            return syntax
        except Exception:
            # Fallback to plain text if language not supported
            return Syntax(code, 'text', theme=theme, line_numbers=line_numbers)
    
    def highlight_diff(self, diff_content: str) -> Text:
        """Highlight git diff content"""
        lines = diff_content.split('\n')
        highlighted = Text()
        
        for line in lines:
            if line.startswith('+++') or line.startswith('---'):
                # File headers
                highlighted.append(line + '\n', style='bold blue')
            elif line.startswith('@@'):
                # Hunk headers
                highlighted.append(line + '\n', style='bold cyan')
            elif line.startswith('+'):
                # Added lines
                highlighted.append(line + '\n', style='green')
            elif line.startswith('-'):
                # Removed lines
                highlighted.append(line + '\n', style='red')
            elif line.startswith('diff --git'):
                # Diff headers
                highlighted.append(line + '\n', style='bold yellow')
            elif line.startswith('index'):
                # Index lines
                highlighted.append(line + '\n', style='dim')
            else:
                # Context lines
                highlighted.append(line + '\n', style='white')
        
        return highlighted
    
    def highlight_log(self, log_content: str, log_type: str = 'general') -> Text:
        """Highlight log content with appropriate colors"""
        lines = log_content.split('\n')
        highlighted = Text()
        
        for line in lines:
            line_lower = line.lower()
            
            if any(keyword in line_lower for keyword in ['error', 'err', 'failed', 'fail']):
                highlighted.append(line + '\n', style='red')
            elif any(keyword in line_lower for keyword in ['warn', 'warning']):
                highlighted.append(line + '\n', style='yellow')
            elif any(keyword in line_lower for keyword in ['info', 'information']):
                highlighted.append(line + '\n', style='blue')
            elif any(keyword in line_lower for keyword in ['debug', 'dbg']):
                highlighted.append(line + '\n', style='dim')
            elif any(keyword in line_lower for keyword in ['success', 'ok', 'done', 'complete']):
                highlighted.append(line + '\n', style='green')
            elif re.match(r'^\d{4}-\d{2}-\d{2}', line):
                # Timestamp lines
                highlighted.append(line + '\n', style='cyan')
            else:
                highlighted.append(line + '\n', style='white')
        
        return highlighted
    
    def highlight_json(self, json_content: str) -> Syntax:
        """Highlight JSON content"""
        return self.highlight_code(json_content, 'json')
    
    def highlight_yaml(self, yaml_content: str) -> Syntax:
        """Highlight YAML content"""
        return self.highlight_code(yaml_content, 'yaml')
    
    def highlight_sql(self, sql_content: str) -> Syntax:
        """Highlight SQL content"""
        return self.highlight_code(sql_content, 'sql')
    
    def highlight_shell(self, shell_content: str) -> Syntax:
        """Highlight shell script content"""
        return self.highlight_code(shell_content, 'bash')
    
    def create_code_block(self, code: str, title: str = "", 
                         language: Optional[str] = None,
                         file_path: Optional[str] = None) -> Syntax:
        """Create a highlighted code block with title"""
        syntax = self.highlight_code(code, language, file_path)
        
        # Rich Syntax doesn't support titles directly, so we return the syntax
        # The caller can wrap it in a Panel with title if needed
        return syntax
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages for syntax highlighting"""
        return list(set(self.language_map.values()))
    
    def detect_language_from_content(self, content: str) -> str:
        """Attempt to detect programming language from content"""
        content_lower = content.lower().strip()
        
        # Python detection
        if any(keyword in content_lower for keyword in [
            'def ', 'import ', 'from ', 'class ', 'if __name__', 'print('
        ]):
            return 'python'
        
        # JavaScript/TypeScript detection
        if any(keyword in content_lower for keyword in [
            'function ', 'const ', 'let ', 'var ', 'console.log', '=>', 'require('
        ]):
            if 'interface ' in content_lower or 'type ' in content_lower:
                return 'typescript'
            return 'javascript'
        
        # HTML detection
        if content_lower.startswith('<!doctype') or '<html' in content_lower:
            return 'html'
        
        # CSS detection
        if re.search(r'[.#][a-z-]+\s*{', content_lower):
            return 'css'
        
        # JSON detection
        if content.strip().startswith('{') and content.strip().endswith('}'):
            try:
                import json
                json.loads(content)
                return 'json'
            except:
                pass
        
        # YAML detection
        if re.search(r'^[a-z_]+:\s*', content_lower, re.MULTILINE):
            return 'yaml'
        
        # SQL detection
        if any(keyword in content_lower for keyword in [
            'select ', 'insert ', 'update ', 'delete ', 'create table', 'alter table'
        ]):
            return 'sql'
        
        # Shell script detection
        if content.startswith('#!') or any(keyword in content_lower for keyword in [
            'echo ', 'cd ', 'ls ', 'grep ', 'awk ', 'sed '
        ]):
            return 'bash'
        
        # Java detection
        if any(keyword in content_lower for keyword in [
            'public class', 'private ', 'protected ', 'public static void main'
        ]):
            return 'java'
        
        # C/C++ detection
        if any(keyword in content_lower for keyword in [
            '#include', 'int main', 'printf(', 'cout <<', 'std::'
        ]):
            if 'std::' in content_lower or 'cout' in content_lower:
                return 'cpp'
            return 'c'
        
        # Go detection
        if any(keyword in content_lower for keyword in [
            'package main', 'func main', 'import "', 'fmt.print'
        ]):
            return 'go'
        
        # Rust detection
        if any(keyword in content_lower for keyword in [
            'fn main', 'let mut', 'println!', 'use std::'
        ]):
            return 'rust'
        
        # Default to text
        return 'text'
    
    def highlight_search_results(self, content: str, search_term: str) -> Text:
        """Highlight search results in content"""
        if not search_term:
            return Text(content)
        
        highlighted = Text()
        content_lower = content.lower()
        search_lower = search_term.lower()
        
        start = 0
        while True:
            pos = content_lower.find(search_lower, start)
            if pos == -1:
                # Add remaining content
                highlighted.append(content[start:])
                break
            
            # Add content before match
            highlighted.append(content[start:pos])
            
            # Add highlighted match
            highlighted.append(content[pos:pos + len(search_term)], style='black on yellow')
            
            start = pos + len(search_term)
        
        return highlighted
    
    def create_line_numbers(self, content: str, start_line: int = 1) -> Text:
        """Create line numbers for content"""
        lines = content.split('\n')
        line_numbers = Text()
        
        max_line = start_line + len(lines) - 1
        width = len(str(max_line))
        
        for i, line in enumerate(lines):
            line_num = start_line + i
            line_numbers.append(f"{line_num:>{width}} ", style="dim")
            if i < len(lines) - 1:
                line_numbers.append("\n")
        
        return line_numbers
    
    def highlight_error_traceback(self, traceback_content: str) -> Text:
        """Highlight Python traceback or error messages"""
        lines = traceback_content.split('\n')
        highlighted = Text()
        
        for line in lines:
            if line.strip().startswith('Traceback'):
                highlighted.append(line + '\n', style='bold red')
            elif line.strip().startswith('File "'):
                # File path in traceback
                highlighted.append(line + '\n', style='blue')
            elif line.strip().startswith('line '):
                # Line number info
                highlighted.append(line + '\n', style='cyan')
            elif any(line.strip().startswith(exc) for exc in [
                'Error:', 'Exception:', 'TypeError:', 'ValueError:', 
                'AttributeError:', 'ImportError:', 'KeyError:', 'IndexError:'
            ]):
                # Exception names
                highlighted.append(line + '\n', style='bold red')
            elif line.strip().startswith('^'):
                # Error pointer
                highlighted.append(line + '\n', style='red')
            else:
                highlighted.append(line + '\n', style='white')
        
        return highlighted
    
    def get_theme_list(self) -> List[str]:
        """Get available syntax highlighting themes"""
        return list(self.themes.keys())
    
    def set_default_theme(self, theme: str) -> bool:
        """Set default theme for syntax highlighting"""
        if theme in self.themes:
            # This would typically save to config
            return True
        return False
