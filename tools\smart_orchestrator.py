"""
Smart Tool Orchestrator - Coordinates all tools intelligently
"""

import asyncio
import json
import time
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

# Import all tool modules
from tools.filesystem_tools import FilesystemTools
from tools.testing_tools import TestingTools
from tools.terminal_tools import TerminalTools
from tools.web_tools import WebTools
from tools.ai_reasoning_tools import AIReasoningTools, Task, TaskStatus
from tools.code_analyzer import CodeAnalyzer
from tools.file_manager import FileManager
from tools.git_manager import GitManager


class ToolCategory(Enum):
    FILESYSTEM = "filesystem"
    TESTING = "testing"
    TERMINAL = "terminal"
    WEB = "web"
    AI_REASONING = "ai_reasoning"
    CODE_ANALYSIS = "code_analysis"
    GIT = "git"


@dataclass
class ToolResult:
    """Result from tool execution"""
    tool_name: str
    success: bool
    result: Dict[str, Any]
    duration: float
    error: Optional[str] = None


class SmartOrchestrator:
    """Intelligent tool orchestration system"""
    
    def __init__(self):
        # Initialize all tool instances
        self.tools = {
            ToolCategory.FILESYSTEM: FilesystemTools(),
            ToolCategory.TESTING: TestingTools(),
            ToolCategory.TERMINAL: TerminalTools(),
            ToolCategory.WEB: WebTools(),
            ToolCategory.AI_REASONING: AIReasoningTools(),
            ToolCategory.CODE_ANALYSIS: CodeAnalyzer(),
            ToolCategory.GIT: GitManager()
        }
        
        # Tool method mappings
        self.tool_methods = self._build_tool_mappings()
        
        # Execution history
        self.execution_history: List[ToolResult] = []
        
        # Smart features
        self.context_awareness = True
        self.auto_tool_selection = True
        self.parallel_execution = True
        
    def _build_tool_mappings(self) -> Dict[str, Callable]:
        """Build comprehensive tool method mappings"""
        mappings = {}
        
        # Filesystem Tools
        fs_tool = self.tools[ToolCategory.FILESYSTEM]
        mappings.update({
            'create_file': fs_tool.create_file,
            'edit_file': fs_tool.edit_file,
            'create_directory': fs_tool.create_directory,
            'read_file': fs_tool.read_file,
            'file_search': fs_tool.file_search,
            'grep_search': fs_tool.grep_search,
            'list_dir': fs_tool.list_dir,
            'semantic_search': fs_tool.semantic_search,
        })
        
        # Testing Tools
        test_tool = self.tools[ToolCategory.TESTING]
        mappings.update({
            'test_search': test_tool.test_search,
            'run_tests': test_tool.run_tests,
            'test_failure': test_tool.test_failure,
            'autonomous_debugger': test_tool.autonomous_debugger,
            'lint_check': test_tool.lint_check,
        })
        
        # Terminal Tools
        term_tool = self.tools[ToolCategory.TERMINAL]
        mappings.update({
            'run_in_terminal': term_tool.run_in_terminal,
            'get_terminal_output': term_tool.get_terminal_output,
            'get_terminal_last_command': term_tool.get_terminal_last_command,
            'create_and_run_task': term_tool.create_and_run_task,
            'get_task_output': term_tool.get_task_output,
            'install_python_packages': term_tool.install_python_packages,
            'configure_python_environment': term_tool.configure_python_environment,
        })
        
        # Web Tools
        web_tool = self.tools[ToolCategory.WEB]
        mappings.update({
            'fetch_webpage': web_tool.fetch_webpage,
            'semantic_web_search': web_tool.semantic_web_search,
            'github_repo_search': web_tool.github_repo_search,
            'retrieval_augmented_generation': web_tool.retrieval_augmented_generation,
        })
        
        # AI Reasoning Tools
        ai_tool = self.tools[ToolCategory.AI_REASONING]
        mappings.update({
            'natural_language_to_code': ai_tool.natural_language_to_code,
            'intent_recognition': ai_tool.intent_recognition,
            'chain_of_thought_reasoning': ai_tool.chain_of_thought_reasoning,
            'multi_step_loop': ai_tool.multi_step_loop,
            'context_tracking_memory': ai_tool.context_tracking_memory,
            'predict_next_code_block': ai_tool.predict_next_code_block,
            'smart_status_report': ai_tool.smart_status_report,
            'auto_tool_invocation': ai_tool.auto_tool_invocation,
        })
        
        # Code Analysis Tools  
        code_tool = self.tools[ToolCategory.CODE_ANALYSIS]
        mappings.update({
            'analyze_file': code_tool.analyze_file,
            'analyze_project': code_tool.analyze_project,
        })
        
        return mappings
    
    def execute_smart_workflow(self, user_request: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute intelligent workflow based on user request"""
        try:
            start_time = time.time()
            workflow_steps = []
            
            # Step 1: Understand user intent
            intent_result = self.tool_methods['intent_recognition'](user_request)
            workflow_steps.append({
                'step': 'intent_recognition',
                'result': intent_result,
                'duration': 0.1
            })
            
            if not intent_result.get('success'):
                return {
                    'success': False,
                    'error': 'Failed to understand user intent',
                    'workflow_steps': workflow_steps
                }
            
            # Step 2: Generate execution plan
            reasoning_result = self.tool_methods['chain_of_thought_reasoning'](user_request, context)
            workflow_steps.append({
                'step': 'reasoning',
                'result': reasoning_result,
                'duration': 0.2
            })
            
            # Step 3: Execute planned actions
            if reasoning_result.get('success'):
                execution_plan = reasoning_result['final_plan']
                execution_results = []
                
                for task in execution_plan:
                    task_result = self._execute_planned_task(task, user_request, context)
                    execution_results.append(task_result)
                    workflow_steps.append({
                        'step': f"execute_{task['task'].lower().replace(' ', '_')}",
                        'result': task_result,
                        'duration': task_result.get('duration', 0)
                    })
            
            total_duration = time.time() - start_time
            
            # Step 4: Generate final report
            report = self._generate_workflow_report(workflow_steps, user_request, total_duration)
            
            return {
                'success': True,
                'user_request': user_request,
                'workflow_steps': workflow_steps,
                'total_duration': total_duration,
                'final_report': report
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'user_request': user_request
            }
    
    def parallel_tool_execution(self, tool_calls: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Execute multiple tools in parallel"""
        try:
            start_time = time.time()
            results = []
            
            # For now, execute sequentially (async would require more complex setup)
            for tool_call in tool_calls:
                tool_name = tool_call.get('tool')
                args = tool_call.get('args', {})
                
                if tool_name in self.tool_methods:
                    try:
                        tool_start = time.time()
                        result = self.tool_methods[tool_name](**args)
                        tool_duration = time.time() - tool_start
                        
                        tool_result = ToolResult(
                            tool_name=tool_name,
                            success=result.get('success', False),
                            result=result,
                            duration=tool_duration
                        )
                        results.append(tool_result)
                        self.execution_history.append(tool_result)
                        
                    except Exception as e:
                        tool_result = ToolResult(
                            tool_name=tool_name,
                            success=False,
                            result={},
                            duration=0,
                            error=str(e)
                        )
                        results.append(tool_result)
                else:
                    results.append(ToolResult(
                        tool_name=tool_name,
                        success=False,
                        result={},
                        duration=0,
                        error=f"Tool not found: {tool_name}"
                    ))
            
            total_duration = time.time() - start_time
            successful_results = [r for r in results if r.success]
            
            return {
                'success': len(successful_results) > 0,
                'total_tools': len(tool_calls),
                'successful_tools': len(successful_results),
                'failed_tools': len(results) - len(successful_results),
                'total_duration': total_duration,
                'results': [asdict(r) for r in results]
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def contextual_tool_suggestion(self, current_state: Dict[str, Any], 
                                  recent_actions: List[str] = None) -> Dict[str, Any]:
        """Suggest next tools based on context"""
        try:
            suggestions = []
            
            # Analyze current state
            if 'error' in str(current_state).lower():
                suggestions.extend([
                    {
                        'tool': 'autonomous_debugger',
                        'reason': 'Error detected in current state',
                        'priority': 'high',
                        'confidence': 0.8
                    },
                    {
                        'tool': 'lint_check',
                        'reason': 'Static analysis might help identify issues',
                        'priority': 'medium',
                        'confidence': 0.6
                    }
                ])
            
            if 'file' in str(current_state).lower():
                suggestions.append({
                    'tool': 'analyze_file',
                    'reason': 'File operations detected',
                    'priority': 'medium',
                    'confidence': 0.7
                })
            
            # Analyze recent actions
            if recent_actions:
                if 'create_file' in recent_actions:
                    suggestions.append({
                        'tool': 'test_search',
                        'reason': 'New file created, might need tests',
                        'priority': 'low',
                        'confidence': 0.5
                    })
                
                if 'edit_file' in recent_actions:
                    suggestions.extend([
                        {
                            'tool': 'run_tests',
                            'reason': 'File edited, should run tests',
                            'priority': 'high',
                            'confidence': 0.8
                        },
                        {
                            'tool': 'lint_check',
                            'reason': 'File edited, check code quality',
                            'priority': 'medium',
                            'confidence': 0.6
                        }
                    ])
            
            # Sort by priority and confidence
            suggestions.sort(key=lambda x: (
                {'high': 3, 'medium': 2, 'low': 1}[x['priority']], 
                x['confidence']
            ), reverse=True)
            
            return {
                'success': True,
                'suggestions': suggestions[:5],  # Top 5 suggestions
                'total_suggestions': len(suggestions)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def adaptive_tool_pipeline(self, goal: str, max_iterations: int = 5) -> Dict[str, Any]:
        """Adaptive pipeline that adjusts based on results"""
        try:
            iterations = []
            current_goal = goal
            
            for i in range(max_iterations):
                iteration_start = time.time()
                
                # Analyze current situation
                analysis = self.tool_methods['intent_recognition'](current_goal)
                
                # Select appropriate tools
                tool_selection = self._select_tools_for_intent(analysis)
                
                # Execute selected tools
                execution_result = self.parallel_tool_execution(tool_selection)
                
                iteration_duration = time.time() - iteration_start
                
                iteration_data = {
                    'iteration': i + 1,
                    'goal': current_goal,
                    'analysis': analysis,
                    'tools_selected': tool_selection,
                    'execution_result': execution_result,
                    'duration': iteration_duration,
                    'success': execution_result.get('success', False)
                }
                
                iterations.append(iteration_data)
                
                # Check if goal is achieved
                if self._is_goal_achieved(execution_result, current_goal):
                    break
                
                # Adapt for next iteration
                current_goal = self._adapt_goal(execution_result, current_goal)
            
            overall_success = any(iteration['success'] for iteration in iterations)
            
            return {
                'success': overall_success,
                'original_goal': goal,
                'iterations': iterations,
                'total_iterations': len(iterations),
                'final_status': 'completed' if overall_success else 'max_iterations_reached'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_tool_analytics(self) -> Dict[str, Any]:
        """Get analytics about tool usage"""
        try:
            if not self.execution_history:
                return {
                    'success': True,
                    'message': 'No tool executions recorded yet'
                }
            
            # Calculate statistics
            total_executions = len(self.execution_history)
            successful_executions = len([r for r in self.execution_history if r.success])
            success_rate = successful_executions / total_executions if total_executions > 0 else 0
            
            # Tool usage frequency
            tool_usage = {}
            total_duration = 0
            
            for result in self.execution_history:
                tool_name = result.tool_name
                tool_usage[tool_name] = tool_usage.get(tool_name, 0) + 1
                total_duration += result.duration
            
            # Most used tools
            most_used = sorted(tool_usage.items(), key=lambda x: x[1], reverse=True)
            
            # Average execution time
            avg_duration = total_duration / total_executions if total_executions > 0 else 0
            
            # Recent performance
            recent_results = self.execution_history[-10:] if len(self.execution_history) >= 10 else self.execution_history
            recent_success_rate = len([r for r in recent_results if r.success]) / len(recent_results) if recent_results else 0
            
            return {
                'success': True,
                'analytics': {
                    'total_executions': total_executions,
                    'successful_executions': successful_executions,
                    'success_rate': round(success_rate * 100, 2),
                    'recent_success_rate': round(recent_success_rate * 100, 2),
                    'average_duration': round(avg_duration, 3),
                    'total_duration': round(total_duration, 3),
                    'most_used_tools': most_used[:5],
                    'available_tools': len(self.tool_methods),
                    'tool_categories': len(self.tools)
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def smart_tool_recommendation(self, context: str, history: List[str] = None) -> Dict[str, Any]:
        """Provide intelligent tool recommendations"""
        try:
            recommendations = []
            
            # Context-based recommendations
            context_lower = context.lower()
            
            # File operations
            if any(word in context_lower for word in ['file', 'create', 'edit', 'read']):
                recommendations.extend([
                    {'tool': 'create_file', 'reason': 'File operations mentioned', 'score': 0.8},
                    {'tool': 'read_file', 'reason': 'Might need to read existing files', 'score': 0.6},
                    {'tool': 'analyze_file', 'reason': 'File analysis could be helpful', 'score': 0.5}
                ])
            
            # Testing operations
            if any(word in context_lower for word in ['test', 'debug', 'error', 'bug']):
                recommendations.extend([
                    {'tool': 'run_tests', 'reason': 'Testing operations mentioned', 'score': 0.9},
                    {'tool': 'autonomous_debugger', 'reason': 'Debugging might be needed', 'score': 0.7},
                    {'tool': 'lint_check', 'reason': 'Code quality check recommended', 'score': 0.6}
                ])
            
            # Web operations
            if any(word in context_lower for word in ['web', 'search', 'url', 'fetch']):
                recommendations.extend([
                    {'tool': 'semantic_web_search', 'reason': 'Web search operations mentioned', 'score': 0.8},
                    {'tool': 'fetch_webpage', 'reason': 'Web content fetching might be needed', 'score': 0.7}
                ])
            
            # Terminal operations
            if any(word in context_lower for word in ['run', 'execute', 'command', 'install']):
                recommendations.extend([
                    {'tool': 'run_in_terminal', 'reason': 'Command execution mentioned', 'score': 0.8},
                    {'tool': 'install_python_packages', 'reason': 'Package installation might be needed', 'score': 0.6}
                ])
            
            # AI reasoning operations
            if any(word in context_lower for word in ['analyze', 'understand', 'plan', 'think']):
                recommendations.extend([
                    {'tool': 'chain_of_thought_reasoning', 'reason': 'Complex reasoning required', 'score': 0.7},
                    {'tool': 'intent_recognition', 'reason': 'Intent analysis needed', 'score': 0.6}
                ])
            
            # Remove duplicates and sort by score
            seen_tools = set()
            unique_recommendations = []
            for rec in recommendations:
                if rec['tool'] not in seen_tools:
                    seen_tools.add(rec['tool'])
                    unique_recommendations.append(rec)
            
            unique_recommendations.sort(key=lambda x: x['score'], reverse=True)
            
            return {
                'success': True,
                'context': context,
                'recommendations': unique_recommendations[:10],  # Top 10
                'total_available_tools': len(self.tool_methods)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    # Helper methods
    def _execute_planned_task(self, task: Dict[str, Any], user_request: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a planned task"""
        task_name = task.get('task', '').lower().replace(' ', '_')
        
        # Map task names to actual tool methods
        task_mappings = {
            'problem_analysis': 'intent_recognition',
            'design_solution': 'chain_of_thought_reasoning',
            'implementation': 'natural_language_to_code',
            'testing': 'run_tests',
            'documentation': 'analyze_file'
        }
        
        tool_method = task_mappings.get(task_name)
        
        if tool_method and tool_method in self.tool_methods:
            try:
                start_time = time.time()
                result = self.tool_methods[tool_method](user_request)
                duration = time.time() - start_time
                
                return {
                    'task_name': task_name,
                    'tool_used': tool_method,
                    'success': result.get('success', False),
                    'result': result,
                    'duration': duration
                }
            except Exception as e:
                return {
                    'task_name': task_name,
                    'tool_used': tool_method,
                    'success': False,
                    'error': str(e),
                    'duration': 0
                }
        else:
            return {
                'task_name': task_name,
                'success': False,
                'error': f'No tool mapping found for task: {task_name}',
                'duration': 0
            }
    
    def _generate_workflow_report(self, workflow_steps: List[Dict[str, Any]], 
                                 user_request: str, total_duration: float) -> Dict[str, Any]:
        """Generate comprehensive workflow report"""
        successful_steps = [step for step in workflow_steps if step['result'].get('success', False)]
        failed_steps = [step for step in workflow_steps if not step['result'].get('success', False)]
        
        return {
            'user_request': user_request,
            'total_steps': len(workflow_steps),
            'successful_steps': len(successful_steps),
            'failed_steps': len(failed_steps),
            'success_rate': len(successful_steps) / max(len(workflow_steps), 1) * 100,
            'total_duration': round(total_duration, 3),
            'average_step_duration': round(total_duration / max(len(workflow_steps), 1), 3),
            'workflow_efficiency': self._calculate_workflow_efficiency(workflow_steps),
            'recommendations': self._generate_workflow_recommendations(workflow_steps)
        }
    
    def _select_tools_for_intent(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Select appropriate tools based on intent analysis"""
        tool_calls = []
        
        if not analysis.get('success'):
            return tool_calls
        
        primary_intent = analysis.get('primary_intent')
        if not primary_intent:
            return tool_calls
        
        intent_name = primary_intent.get('intent', '')
        
        # Map intents to tool sequences
        intent_tool_mapping = {
            'create_file': [
                {'tool': 'create_file', 'args': {'file_path': 'example.py', 'content': '# Generated file'}}
            ],
            'edit_file': [
                {'tool': 'read_file', 'args': {'file_path': 'example.py'}},
                {'tool': 'analyze_file', 'args': {'file_path': 'example.py'}}
            ],
            'debug': [
                {'tool': 'autonomous_debugger', 'args': {'file_path': 'example.py'}},
                {'tool': 'lint_check', 'args': {'file_path': 'example.py'}}
            ],
            'test': [
                {'tool': 'test_search', 'args': {'source_file': 'example.py'}},
                {'tool': 'run_tests', 'args': {'test_path': '.'}}
            ]
        }
        
        return intent_tool_mapping.get(intent_name, [])
    
    def _is_goal_achieved(self, execution_result: Dict[str, Any], goal: str) -> bool:
        """Check if the goal has been achieved"""
        # Simple heuristic - if most tools succeeded, consider goal achieved
        success_rate = execution_result.get('successful_tools', 0) / max(execution_result.get('total_tools', 1), 1)
        return success_rate >= 0.7
    
    def _adapt_goal(self, execution_result: Dict[str, Any], current_goal: str) -> str:
        """Adapt goal based on execution results"""
        failed_tools = execution_result.get('failed_tools', 0)
        
        if failed_tools > 0:
            return f"Fix issues and retry: {current_goal}"
        else:
            return f"Optimize and enhance: {current_goal}"
    
    def _calculate_workflow_efficiency(self, workflow_steps: List[Dict[str, Any]]) -> float:
        """Calculate workflow efficiency score"""
        if not workflow_steps:
            return 0.0
        
        success_count = sum(1 for step in workflow_steps if step['result'].get('success', False))
        total_steps = len(workflow_steps)
        
        # Base efficiency on success rate
        efficiency = success_count / total_steps
        
        # Adjust for duration (faster is better)
        avg_duration = sum(step.get('duration', 0) for step in workflow_steps) / total_steps
        if avg_duration < 1.0:  # Less than 1 second per step is good
            efficiency *= 1.1  # 10% bonus
        elif avg_duration > 5.0:  # More than 5 seconds per step is slow
            efficiency *= 0.9  # 10% penalty
        
        return min(efficiency, 1.0)  # Cap at 1.0
    
    def _generate_workflow_recommendations(self, workflow_steps: List[Dict[str, Any]]) -> List[str]:
        """Generate recommendations for workflow improvement"""
        recommendations = []
        
        failed_steps = [step for step in workflow_steps if not step['result'].get('success', False)]
        
        if failed_steps:
            recommendations.append(f"Address {len(failed_steps)} failed steps to improve success rate")
        
        slow_steps = [step for step in workflow_steps if step.get('duration', 0) > 5.0]
        if slow_steps:
            recommendations.append(f"Optimize {len(slow_steps)} slow-running steps")
        
        if len(workflow_steps) > 10:
            recommendations.append("Consider breaking down into smaller workflows")
        
        if not recommendations:
            recommendations.append("Workflow executed efficiently")
        
        return recommendations