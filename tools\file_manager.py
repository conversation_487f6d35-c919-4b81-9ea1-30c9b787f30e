"""
File management utilities for AI Coding Assistant
"""

import os
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
import mimetypes
import fnmatch

class FileManager:
    """File management utilities"""
    
    def __init__(self, root_path: str = "."):
        self.root_path = Path(root_path).resolve()
        self.ignored_patterns = {
            '*.pyc', '__pycache__', '.git', '.gitignore', 
            'node_modules', '.env', '*.log', '.DS_Store',
            '.vscode', '.idea', '*.tmp', '*.temp'
        }
        
        # Common code file extensions
        self.code_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.cpp', '.c', '.h',
            '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
            '.html', '.css', '.scss', '.sass', '.less', '.xml', '.json',
            '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf', '.md',
            '.sql', '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat'
        }
    
    def get_file_tree(self, max_depth: int = 3, show_hidden: bool = False) -> Dict[str, Any]:
        """Get file tree structure"""
        def build_tree(path: Path, current_depth: int = 0) -> Dict[str, Any]:
            if current_depth > max_depth:
                return {}
            
            tree = {
                'name': path.name,
                'path': str(path.relative_to(self.root_path)),
                'type': 'directory' if path.is_dir() else 'file',
                'size': path.stat().st_size if path.is_file() else 0,
                'children': []
            }
            
            if path.is_dir():
                try:
                    for item in sorted(path.iterdir()):
                        # Skip hidden files unless requested
                        if not show_hidden and item.name.startswith('.'):
                            continue
                        
                        # Skip ignored patterns
                        if self._should_ignore(item.name):
                            continue
                        
                        child_tree = build_tree(item, current_depth + 1)
                        if child_tree:
                            tree['children'].append(child_tree)
                except PermissionError:
                    pass
            
            return tree
        
        return build_tree(self.root_path)
    
    def _should_ignore(self, filename: str) -> bool:
        """Check if file should be ignored"""
        for pattern in self.ignored_patterns:
            if fnmatch.fnmatch(filename, pattern):
                return True
        return False
    
    def read_file(self, file_path: str) -> str:
        """Read file content safely"""
        try:
            path = Path(file_path)
            if not path.is_absolute():
                path = self.root_path / path
            
            # Security check - ensure path is within root
            path.resolve().relative_to(self.root_path.resolve())
            
            with open(path, 'r', encoding='utf-8', errors='ignore') as f:
                return f.read()
        except Exception as e:
            raise Exception(f"Error reading file {file_path}: {e}")
    
    def write_file(self, file_path: str, content: str, create_dirs: bool = True) -> None:
        """Write content to file safely"""
        try:
            path = Path(file_path)
            if not path.is_absolute():
                path = self.root_path / path
            
            # Security check - ensure path is within root
            path.resolve().relative_to(self.root_path.resolve())
            
            if create_dirs:
                path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(path, 'w', encoding='utf-8') as f:
                f.write(content)
        except Exception as e:
            raise Exception(f"Error writing file {file_path}: {e}")
    
    def backup_file(self, file_path: str) -> str:
        """Create backup of file"""
        try:
            path = Path(file_path)
            if not path.is_absolute():
                path = self.root_path / path
            
            if not path.exists():
                raise Exception(f"File does not exist: {file_path}")
            
            backup_path = path.with_suffix(path.suffix + '.backup')
            counter = 1
            while backup_path.exists():
                backup_path = path.with_suffix(f'{path.suffix}.backup.{counter}')
                counter += 1
            
            shutil.copy2(path, backup_path)
            return str(backup_path.relative_to(self.root_path))
            
        except Exception as e:
            raise Exception(f"Error creating backup for {file_path}: {e}")
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Get detailed file information"""
        try:
            path = Path(file_path)
            if not path.is_absolute():
                path = self.root_path / path
            
            if not path.exists():
                raise Exception(f"File does not exist: {file_path}")
            
            stat = path.stat()
            mime_type, _ = mimetypes.guess_type(str(path))
            
            return {
                'name': path.name,
                'path': str(path.relative_to(self.root_path)),
                'absolute_path': str(path),
                'size': stat.st_size,
                'modified': stat.st_mtime,
                'is_file': path.is_file(),
                'is_directory': path.is_dir(),
                'extension': path.suffix,
                'mime_type': mime_type,
                'is_code_file': path.suffix.lower() in self.code_extensions,
                'is_text_file': self._is_text_file(path)
            }
            
        except Exception as e:
            raise Exception(f"Error getting file info for {file_path}: {e}")
    
    def _is_text_file(self, path: Path) -> bool:
        """Check if file is a text file"""
        if path.suffix.lower() in self.code_extensions:
            return True
        
        try:
            with open(path, 'rb') as f:
                chunk = f.read(1024)
                if b'\0' in chunk:
                    return False
                return True
        except:
            return False
    
    def find_files(self, pattern: str = "*", file_type: str = "all", max_results: int = 100) -> List[Dict[str, Any]]:
        """Find files matching pattern"""
        results = []
        count = 0
        
        for path in self.root_path.rglob(pattern):
            if count >= max_results:
                break
            
            if self._should_ignore(path.name):
                continue
            
            if file_type == "files" and not path.is_file():
                continue
            elif file_type == "directories" and not path.is_dir():
                continue
            elif file_type == "code" and not (path.is_file() and path.suffix.lower() in self.code_extensions):
                continue
            
            try:
                results.append(self.get_file_info(str(path.relative_to(self.root_path))))
                count += 1
            except:
                continue
        
        return results
    
    def get_code_files(self, max_results: int = 50) -> List[Dict[str, Any]]:
        """Get list of code files in project"""
        code_files = []
        count = 0
        
        for path in self.root_path.rglob("*"):
            if count >= max_results:
                break
            
            if not path.is_file():
                continue
            
            if self._should_ignore(path.name):
                continue
            
            if path.suffix.lower() in self.code_extensions:
                try:
                    code_files.append(self.get_file_info(str(path.relative_to(self.root_path))))
                    count += 1
                except:
                    continue
        
        return code_files
    
    def get_project_structure(self) -> Dict[str, Any]:
        """Get overall project structure analysis"""
        structure = {
            'total_files': 0,
            'total_directories': 0,
            'code_files': 0,
            'file_types': {},
            'languages': {},
            'largest_files': [],
            'recently_modified': []
        }
        
        all_files = []
        
        for path in self.root_path.rglob("*"):
            if self._should_ignore(path.name):
                continue
            
            if path.is_file():
                structure['total_files'] += 1
                
                # File type analysis
                ext = path.suffix.lower()
                structure['file_types'][ext] = structure['file_types'].get(ext, 0) + 1
                
                # Language detection
                if ext in self.code_extensions:
                    structure['code_files'] += 1
                    lang = self._get_language_from_extension(ext)
                    structure['languages'][lang] = structure['languages'].get(lang, 0) + 1
                
                # File info for sorting
                try:
                    stat = path.stat()
                    all_files.append({
                        'path': str(path.relative_to(self.root_path)),
                        'size': stat.st_size,
                        'modified': stat.st_mtime
                    })
                except:
                    continue
                    
            elif path.is_dir():
                structure['total_directories'] += 1
        
        # Sort for largest and recently modified
        all_files.sort(key=lambda x: x['size'], reverse=True)
        structure['largest_files'] = all_files[:10]
        
        all_files.sort(key=lambda x: x['modified'], reverse=True)
        structure['recently_modified'] = all_files[:10]
        
        return structure
    
    def _get_language_from_extension(self, ext: str) -> str:
        """Map file extension to programming language"""
        language_map = {
            '.py': 'Python',
            '.js': 'JavaScript',
            '.ts': 'TypeScript',
            '.jsx': 'React',
            '.tsx': 'React TypeScript',
            '.java': 'Java',
            '.cpp': 'C++',
            '.c': 'C',
            '.h': 'C/C++ Header',
            '.cs': 'C#',
            '.php': 'PHP',
            '.rb': 'Ruby',
            '.go': 'Go',
            '.rs': 'Rust',
            '.swift': 'Swift',
            '.kt': 'Kotlin',
            '.scala': 'Scala',
            '.html': 'HTML',
            '.css': 'CSS',
            '.scss': 'SCSS',
            '.sass': 'Sass',
            '.less': 'Less',
            '.xml': 'XML',
            '.json': 'JSON',
            '.yaml': 'YAML',
            '.yml': 'YAML',
            '.toml': 'TOML',
            '.md': 'Markdown',
            '.sql': 'SQL',
            '.sh': 'Shell',
            '.bash': 'Bash',
            '.ps1': 'PowerShell'
        }
        
        return language_map.get(ext, 'Unknown')
