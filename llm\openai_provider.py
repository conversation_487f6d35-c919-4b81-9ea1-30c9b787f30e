"""
OpenAI LLM provider implementation
"""

import os
from typing import Dict, List, Any, Optional
from openai import OpenAI

from llm.base import LL<PERSON>rovider

class OpenAIProvider(LLMProvider):
    """OpenAI LLM provider"""
    
    def _initialize_client(self) -> None:
        """Initialize OpenAI client"""
        try:
            self.client = OpenAI(api_key=self.api_key)
        except Exception as e:
            raise Exception(f"Failed to initialize OpenAI client: {e}")
    
    def get_response(self, prompt: str, model: Optional[str] = None, **kwargs) -> str:
        """Get response from OpenAI"""
        if not self.client:
            raise Exception("OpenAI client not initialized")
        
        # the newest OpenAI model is "gpt-4o" which was released May 13, 2024.
        # do not change this unless explicitly requested by the user
        model_name = model or self.get_default_model()
        
        try:
            # Handle conversation history if provided
            messages = kwargs.get('messages', [])
            
            if messages:
                # Use provided conversation history
                openai_messages = []
                for msg in messages:
                    openai_messages.append({
                        'role': msg.get('role', 'user'),
                        'content': msg.get('content', '')
                    })
                
                # Add current prompt
                openai_messages.append({
                    'role': 'user',
                    'content': prompt
                })
            else:
                # Simple prompt
                openai_messages = [
                    {'role': 'user', 'content': prompt}
                ]
            
            response = self.client.chat.completions.create(
                model=model_name,
                messages=openai_messages,  # type: ignore
                max_tokens=kwargs.get('max_tokens', 4000),
                temperature=kwargs.get('temperature', 0.7)
            )
            
            return response.choices[0].message.content or "No response generated"
            
        except Exception as e:
            raise Exception(f"OpenAI API error: {e}")
    
    def get_available_models(self) -> List[str]:
        """Get available OpenAI models"""
        return self.config.get('models', [
            'gpt-4o',
            'gpt-4o-mini',
            'gpt-4-turbo',
            'gpt-3.5-turbo'
        ])
    
    def test_connection(self) -> bool:
        """Test OpenAI API connection"""
        try:
            response = self.client.chat.completions.create(
                model=self.get_default_model(),
                messages=[
                    {'role': 'user', 'content': 'Hello, this is a test. Please respond with "Test successful".'}
                ],
                max_tokens=10
            )
            return bool(response.choices[0].message.content)
        except Exception:
            return False
    
    def analyze_code(self, code: str, language: str = "", model: Optional[str] = None) -> str:
        """Analyze code with OpenAI"""
        system_prompt = f"""You are an expert {language} developer. Analyze the provided code and give detailed feedback on:
1. Code quality and structure
2. Potential bugs or issues
3. Performance improvements
4. Best practices and conventions
5. Security considerations"""
        
        messages = [
            {'role': 'system', 'content': system_prompt},
            {'role': 'user', 'content': f"Analyze this {language} code:\n\n```{language}\n{code}\n```"}
        ]
        
        return self.get_response("", model, messages=messages)
    
    def suggest_improvements(self, code: str, context: str = "", model: Optional[str] = None) -> str:
        """Suggest code improvements"""
        system_prompt = "You are an expert software developer. Provide specific, actionable improvement suggestions for the given code."
        
        user_prompt = f"""Context: {context}

Code to improve:
```
{code}
```

Provide specific suggestions for:
1. Code readability and maintainability
2. Performance optimizations
3. Error handling improvements
4. Best practices adherence
5. Architecture improvements"""
        
        messages = [
            {'role': 'system', 'content': system_prompt},
            {'role': 'user', 'content': user_prompt}
        ]
        
        return self.get_response("", model, messages=messages)
