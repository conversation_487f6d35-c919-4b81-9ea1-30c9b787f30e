"""
Code analysis utilities for AI Coding Assistant
"""

import ast
import re
import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Tuple
import mimetypes

class CodeAnalyzer:
    """Advanced code analysis utilities"""
    
    def __init__(self):
        self.supported_languages = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'react',
            '.tsx': 'react_typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.cs': 'csharp',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go',
            '.rs': 'rust',
            '.swift': 'swift',
            '.kt': 'kotlin'
        }
    
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """Analyze a single code file"""
        path = Path(file_path)
        
        if not path.exists():
            raise Exception(f"File not found: {file_path}")
        
        try:
            with open(path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except Exception as e:
            raise Exception(f"Error reading file: {e}")
        
        extension = path.suffix.lower()
        language = self.supported_languages.get(extension, 'unknown')
        
        analysis = {
            'file_path': str(path),
            'language': language,
            'extension': extension,
            'size_bytes': len(content.encode('utf-8')),
            'line_count': content.count('\n') + 1,
            'char_count': len(content),
            'functions': [],
            'classes': [],
            'imports': [],
            'comments': [],
            'complexity_score': 0,
            'issues': [],
            'suggestions': []
        }
        
        # Language-specific analysis
        if language == 'python':
            analysis.update(self._analyze_python(content))
        elif language in ['javascript', 'typescript']:
            analysis.update(self._analyze_javascript(content))
        else:
            analysis.update(self._analyze_generic(content))
        
        # Common analysis
        analysis['complexity_score'] = self._calculate_complexity(content, language)
        analysis['issues'].extend(self._find_common_issues(content, language))
        
        return analysis
    
    def _analyze_python(self, content: str) -> Dict[str, Any]:
        """Analyze Python code specifically"""
        analysis = {
            'functions': [],
            'classes': [],
            'imports': [],
            'comments': [],
            'docstrings': [],
            'decorators': []
        }
        
        try:
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    func_info = {
                        'name': node.name,
                        'line': node.lineno,
                        'args': [arg.arg for arg in node.args.args],
                        'decorators': [ast.unparse(d) for d in node.decorator_list],
                        'docstring': ast.get_docstring(node),
                        'returns': ast.unparse(node.returns) if node.returns else None
                    }
                    analysis['functions'].append(func_info)
                
                elif isinstance(node, ast.ClassDef):
                    class_info = {
                        'name': node.name,
                        'line': node.lineno,
                        'bases': [ast.unparse(base) for base in node.bases],
                        'decorators': [ast.unparse(d) for d in node.decorator_list],
                        'docstring': ast.get_docstring(node),
                        'methods': []
                    }
                    
                    # Find methods in class
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            class_info['methods'].append(item.name)
                    
                    analysis['classes'].append(class_info)
                
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            analysis['imports'].append({
                                'module': alias.name,
                                'alias': alias.asname,
                                'line': node.lineno,
                                'type': 'import'
                            })
                    else:  # ImportFrom
                        for alias in node.names:
                            analysis['imports'].append({
                                'module': node.module,
                                'name': alias.name,
                                'alias': alias.asname,
                                'line': node.lineno,
                                'type': 'from_import'
                            })
        
        except SyntaxError as e:
            analysis['syntax_error'] = str({
                'message': str(e),
                'line': e.lineno,
                'offset': e.offset
            })
        
        # Find comments
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            if stripped.startswith('#'):
                analysis['comments'].append({
                    'line': i,
                    'text': stripped[1:].strip()
                })
        
        return analysis
    
    def _analyze_javascript(self, content: str) -> Dict[str, Any]:
        """Analyze JavaScript/TypeScript code"""
        analysis = {
            'functions': [],
            'classes': [],
            'imports': [],
            'exports': [],
            'comments': []
        }
        
        lines = content.split('\n')
        
        # Simple regex patterns for JavaScript analysis
        function_pattern = re.compile(r'(?:function\s+(\w+)|(\w+)\s*[:=]\s*(?:function|\([^)]*\)\s*=>))')
        class_pattern = re.compile(r'class\s+(\w+)')
        import_pattern = re.compile(r'import\s+(?:{[^}]+}|\w+|[*])\s+from\s+[\'"]([^\'"]+)[\'"]')
        export_pattern = re.compile(r'export\s+(?:default\s+)?(?:function\s+(\w+)|class\s+(\w+)|const\s+(\w+))')
        
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            
            # Functions
            func_match = function_pattern.search(stripped)
            if func_match:
                func_name = func_match.group(1) or func_match.group(2)
                if func_name:
                    analysis['functions'].append({
                        'name': func_name,
                        'line': i,
                        'type': 'function'
                    })
            
            # Classes
            class_match = class_pattern.search(stripped)
            if class_match:
                analysis['classes'].append({
                    'name': class_match.group(1),
                    'line': i
                })
            
            # Imports
            import_match = import_pattern.search(stripped)
            if import_match:
                analysis['imports'].append({
                    'module': import_match.group(1),
                    'line': i
                })
            
            # Exports
            export_match = export_pattern.search(stripped)
            if export_match:
                export_name = export_match.group(1) or export_match.group(2) or export_match.group(3)
                if export_name:
                    analysis['exports'].append({
                        'name': export_name,
                        'line': i
                    })
            
            # Comments
            if stripped.startswith('//'):
                analysis['comments'].append({
                    'line': i,
                    'text': stripped[2:].strip(),
                    'type': 'single_line'
                })
        
        return analysis
    
    def _analyze_generic(self, content: str) -> Dict[str, Any]:
        """Generic analysis for unsupported languages"""
        analysis = {
            'functions': [],
            'classes': [],
            'imports': [],
            'comments': []
        }
        
        lines = content.split('\n')
        
        # Simple pattern matching for common constructs
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            
            # Common comment patterns
            if any(stripped.startswith(pattern) for pattern in ['//', '#', '--', '%', ';']):
                analysis['comments'].append({
                    'line': i,
                    'text': stripped,
                    'type': 'comment'
                })
        
        return analysis
    
    def _calculate_complexity(self, content: str, language: str) -> int:
        """Calculate cyclomatic complexity estimate"""
        complexity = 1  # Base complexity
        
        # Count control flow statements
        control_patterns = [
            r'\bif\b', r'\belse\b', r'\belif\b', r'\bwhile\b', r'\bfor\b',
            r'\btry\b', r'\bcatch\b', r'\bswitch\b', r'\bcase\b',
            r'\band\b', r'\bor\b', r'\&&', r'\|\|', r'\?'
        ]
        
        for pattern in control_patterns:
            complexity += len(re.findall(pattern, content, re.IGNORECASE))
        
        return min(complexity, 100)  # Cap at 100
    
    def _find_common_issues(self, content: str, language: str) -> List[Dict[str, Any]]:
        """Find common code issues"""
        issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            
            # Long lines
            if len(line) > 120:
                issues.append({
                    'type': 'style',
                    'severity': 'warning',
                    'line': i,
                    'message': f'Line too long ({len(line)} characters)',
                    'suggestion': 'Consider breaking long lines for better readability'
                })
            
            # TODO comments
            if 'TODO' in line.upper() or 'FIXME' in line.upper():
                issues.append({
                    'type': 'todo',
                    'severity': 'info',
                    'line': i,
                    'message': 'TODO/FIXME comment found',
                    'suggestion': 'Consider addressing this TODO item'
                })
            
            # Trailing whitespace
            if line.endswith(' ') or line.endswith('\t'):
                issues.append({
                    'type': 'style',
                    'severity': 'minor',
                    'line': i,
                    'message': 'Trailing whitespace',
                    'suggestion': 'Remove trailing whitespace'
                })
        
        # Language-specific issues
        if language == 'python':
            issues.extend(self._find_python_issues(content, lines))
        elif language in ['javascript', 'typescript']:
            issues.extend(self._find_javascript_issues(content, lines))
        
        return issues
    
    def _find_python_issues(self, content: str, lines: List[str]) -> List[Dict[str, Any]]:
        """Find Python-specific issues"""
        issues = []
        
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            
            # Use of print() (might want logging instead)
            if re.search(r'\bprint\s*\(', stripped):
                issues.append({
                    'type': 'best_practice',
                    'severity': 'info',
                    'line': i,
                    'message': 'Use of print() function',
                    'suggestion': 'Consider using logging instead of print for production code'
                })
            
            # Bare except clauses
            if stripped == 'except:':
                issues.append({
                    'type': 'error_handling',
                    'severity': 'warning',
                    'line': i,
                    'message': 'Bare except clause',
                    'suggestion': 'Specify exception types or use "except Exception:"'
                })
        
        return issues
    
    def _find_javascript_issues(self, content: str, lines: List[str]) -> List[Dict[str, Any]]:
        """Find JavaScript-specific issues"""
        issues = []
        
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            
            # Use of var instead of let/const
            if re.search(r'\bvar\s+', stripped):
                issues.append({
                    'type': 'best_practice',
                    'severity': 'warning',
                    'line': i,
                    'message': 'Use of var keyword',
                    'suggestion': 'Consider using let or const instead of var'
                })
            
            # console.log in production
            if 'console.log(' in stripped:
                issues.append({
                    'type': 'debug',
                    'severity': 'info',
                    'line': i,
                    'message': 'console.log() statement',
                    'suggestion': 'Remove debug console.log statements before production'
                })
        
        return issues
    
    def analyze_project(self, project_path: str = ".", max_files: int = 50) -> Dict[str, Any]:
        """Analyze entire project/directory"""
        project_path_obj = Path(project_path)
        
        if not project_path_obj.exists():
            raise Exception(f"Project path does not exist: {project_path}")
        
        analysis = {
            'project_path': str(project_path_obj),
            'total_files': 0,
            'analyzed_files': 0,
            'languages': {},
            'total_lines': 0,
            'total_functions': 0,
            'total_classes': 0,
            'complexity_distribution': {},
            'issues_summary': {
                'error': 0,
                'warning': 0,
                'info': 0,
                'minor': 0
            },
            'files': []
        }
        
        # Find code files
        code_files = []
        for ext in self.supported_languages:
            pattern = f"**/*{ext}"
            files = list(project_path_obj.glob(pattern))
            code_files.extend(files[:max_files])
        
        analysis['total_files'] = len(code_files)
        
        # Analyze each file
        for file_path in code_files[:max_files]:
            try:
                file_analysis = self.analyze_file(str(file_path))
                analysis['files'].append(file_analysis)
                analysis['analyzed_files'] += 1
                
                # Aggregate statistics
                language = file_analysis['language']
                analysis['languages'][language] = analysis['languages'].get(language, 0) + 1
                analysis['total_lines'] += file_analysis['line_count']
                analysis['total_functions'] += len(file_analysis['functions'])
                analysis['total_classes'] += len(file_analysis['classes'])
                
                # Complexity distribution
                complexity = file_analysis['complexity_score']
                complexity_range = self._get_complexity_range(complexity)
                analysis['complexity_distribution'][complexity_range] = \
                    analysis['complexity_distribution'].get(complexity_range, 0) + 1
                
                # Issues summary
                for issue in file_analysis['issues']:
                    severity = issue['severity']
                    analysis['issues_summary'][severity] = \
                        analysis['issues_summary'].get(severity, 0) + 1
                        
            except Exception as e:
                # Log error but continue with other files
                print(f"Error analyzing {file_path}: {e}")
        
        return analysis
    
    def _get_complexity_range(self, complexity: int) -> str:
        """Get complexity range category"""
        if complexity <= 5:
            return 'low'
        elif complexity <= 10:
            return 'medium'
        elif complexity <= 20:
            return 'high'
        else:
            return 'very_high'
    
    def get_file_dependencies(self, file_path: str) -> List[Dict[str, Any]]:
        """Analyze file dependencies"""
        try:
            analysis = self.analyze_file(file_path)
            dependencies = []
            
            for imp in analysis.get('imports', []):
                dependencies.append({
                    'type': 'import',
                    'module': imp.get('module', ''),
                    'name': imp.get('name', ''),
                    'line': imp.get('line', 0)
                })
            
            return dependencies
            
        except Exception as e:
            return [{'error': str(e)}]
    
    def suggest_refactoring(self, file_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Suggest refactoring opportunities"""
        suggestions = []
        
        # Large functions
        for func in file_analysis.get('functions', []):
            if len(func.get('args', [])) > 5:
                suggestions.append({
                    'type': 'refactor',
                    'target': f"Function '{func['name']}'",
                    'issue': 'Too many parameters',
                    'suggestion': 'Consider using a configuration object or breaking into smaller functions'
                })
        
        # High complexity
        if file_analysis.get('complexity_score', 0) > 15:
            suggestions.append({
                'type': 'refactor',
                'target': 'File',
                'issue': 'High cyclomatic complexity',
                'suggestion': 'Consider breaking complex logic into smaller, more focused functions'
            })
        
        # Too many classes in one file
        if len(file_analysis.get('classes', [])) > 3:
            suggestions.append({
                'type': 'organization',
                'target': 'File structure',
                'issue': 'Many classes in one file',
                'suggestion': 'Consider splitting classes into separate modules'
            })
        
        return suggestions
