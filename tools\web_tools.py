"""
Web and Search Tools for AI Coding Assistant
"""

import requests
import json
import re
import urllib.parse
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass


@dataclass
class SearchResult:
    """Web search result"""
    title: str
    url: str
    snippet: str
    source: str


class WebTools:
    """Web scraping and search utilities"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def fetch_webpage(self, url: str, timeout: int = 10, 
                     extract_text: bool = True) -> Dict[str, Any]:
        """Scrape webpage content"""
        try:
            response = self.session.get(url, timeout=timeout)
            response.raise_for_status()
            
            content_type = response.headers.get('content-type', '').lower()
            
            result = {
                'success': True,
                'url': url,
                'status_code': response.status_code,
                'content_type': content_type,
                'size': len(response.content),
                'headers': dict(response.headers)
            }
            
            if 'text/html' in content_type:
                result['html'] = response.text
                
                if extract_text:
                    # Simple text extraction (in production, use BeautifulSoup)
                    text = self._extract_text_from_html(response.text)
                    result['text'] = text
                    result['word_count'] = len(text.split())
                    
                # Extract metadata
                result['metadata'] = self._extract_html_metadata(response.text)
                
            elif 'application/json' in content_type:
                try:
                    result['json'] = response.json()
                except json.JSONDecodeError:
                    result['text'] = response.text
            else:
                result['text'] = response.text
            
            return result
            
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': str(e),
                'url': url
            }
    
    def semantic_web_search(self, query: str, search_engine: str = "duckduckgo",
                           max_results: int = 10) -> Dict[str, Any]:
        """Natural language Google/Bing/Web search"""
        try:
            if search_engine.lower() == "duckduckgo":
                results = self._search_duckduckgo(query, max_results)
            elif search_engine.lower() == "github":
                results = self._search_github(query, max_results)
            else:
                return {
                    'success': False,
                    'error': f'Unsupported search engine: {search_engine}'
                }
            
            return {
                'success': True,
                'query': query,
                'search_engine': search_engine,
                'results_count': len(results),
                'results': results
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def github_repo_search(self, query: str, language: str = None,
                          sort: str = "stars", max_results: int = 10) -> Dict[str, Any]:
        """Search GitHub repositories"""
        try:
            # Build GitHub API search query
            search_query = query
            if language:
                search_query += f" language:{language}"
            
            params = {
                'q': search_query,
                'sort': sort,
                'order': 'desc',
                'per_page': min(max_results, 100)
            }
            
            response = self.session.get(
                'https://api.github.com/search/repositories',
                params=params,
                timeout=10
            )
            response.raise_for_status()
            
            data = response.json()
            repositories = []
            
            for repo in data.get('items', []):
                repositories.append({
                    'name': repo['full_name'],
                    'description': repo.get('description', ''),
                    'url': repo['html_url'],
                    'clone_url': repo['clone_url'],
                    'stars': repo['stargazers_count'],
                    'forks': repo['forks_count'],
                    'language': repo.get('language'),
                    'updated': repo['updated_at'],
                    'size': repo['size']
                })
            
            return {
                'success': True,
                'query': query,
                'total_count': data.get('total_count', 0),
                'results_returned': len(repositories),
                'repositories': repositories
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def retrieval_augmented_generation(self, urls: List[str], 
                                     extract_content: bool = True) -> Dict[str, Any]:
        """Extract and summarize content from multiple sources"""
        try:
            sources = []
            all_content = []
            
            for url in urls:
                result = self.fetch_webpage(url, extract_text=extract_content)
                
                if result['success']:
                    source_info = {
                        'url': url,
                        'title': result.get('metadata', {}).get('title', 'Unknown'),
                        'status': 'success',
                        'word_count': result.get('word_count', 0)
                    }
                    
                    if extract_content and 'text' in result:
                        content = result['text']
                        source_info['content'] = content
                        all_content.append(content)
                    
                    sources.append(source_info)
                else:
                    sources.append({
                        'url': url,
                        'status': 'failed',
                        'error': result.get('error', 'Unknown error')
                    })
            
            # Generate summary statistics
            combined_content = '\n\n'.join(all_content)
            
            return {
                'success': True,
                'sources_processed': len(urls),
                'sources_successful': len([s for s in sources if s.get('status') == 'success']),
                'total_content_length': len(combined_content),
                'total_words': len(combined_content.split()),
                'sources': sources,
                'combined_content': combined_content if extract_content else None
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _extract_text_from_html(self, html: str) -> str:
        """Extract text content from HTML (basic implementation)"""
        # Remove scripts and styles
        html = re.sub(r'<script[^>]*>.*?</script>', '', html, flags=re.DOTALL | re.IGNORECASE)
        html = re.sub(r'<style[^>]*>.*?</style>', '', html, flags=re.DOTALL | re.IGNORECASE)
        
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', '', html)
        
        # Clean up whitespace
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        return text
    
    def _extract_html_metadata(self, html: str) -> Dict[str, str]:
        """Extract metadata from HTML"""
        metadata = {}
        
        # Extract title
        title_match = re.search(r'<title[^>]*>(.*?)</title>', html, re.IGNORECASE | re.DOTALL)
        if title_match:
            metadata['title'] = title_match.group(1).strip()
        
        # Extract meta tags
        meta_tags = re.findall(r'<meta[^>]+>', html, re.IGNORECASE)
        for tag in meta_tags:
            name_match = re.search(r'name=["\']([^"\']+)["\']', tag, re.IGNORECASE)
            content_match = re.search(r'content=["\']([^"\']+)["\']', tag, re.IGNORECASE)
            
            if name_match and content_match:
                metadata[name_match.group(1)] = content_match.group(1)
        
        return metadata
    
    def _search_duckduckgo(self, query: str, max_results: int) -> List[SearchResult]:
        """Search using DuckDuckGo"""
        results = []
        
        try:
            # DuckDuckGo instant answers API (limited but free)
            params = {
                'q': query,
                'format': 'json',
                'no_redirect': '1',
                'no_html': '1',
                'skip_disambig': '1'
            }
            
            response = self.session.get(
                'https://api.duckduckgo.com/',
                params=params,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Extract instant answer
                if data.get('Abstract'):
                    results.append(SearchResult(
                        title=data.get('Heading', 'DuckDuckGo Result'),
                        url=data.get('AbstractURL', ''),
                        snippet=data.get('Abstract', ''),
                        source='duckduckgo'
                    ))
                
                # Extract related topics
                for topic in data.get('RelatedTopics', [])[:max_results-1]:
                    if isinstance(topic, dict) and 'Text' in topic:
                        results.append(SearchResult(
                            title=topic.get('Text', '').split(' - ')[0],
                            url=topic.get('FirstURL', ''),
                            snippet=topic.get('Text', ''),
                            source='duckduckgo'
                        ))
            
        except Exception:
            pass  # Fallback to empty results
        
        return results[:max_results]
    
    def _search_github(self, query: str, max_results: int) -> List[SearchResult]:
        """Search GitHub code"""
        results = []
        
        try:
            params = {
                'q': query,
                'sort': 'stars',
                'order': 'desc',
                'per_page': min(max_results, 30)
            }
            
            response = self.session.get(
                'https://api.github.com/search/code',
                params=params,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                
                for item in data.get('items', []):
                    results.append(SearchResult(
                        title=f"{item['repository']['full_name']}/{item['name']}",
                        url=item['html_url'],
                        snippet=item.get('text_matches', [{}])[0].get('fragment', ''),
                        source='github'
                    ))
                    
        except Exception:
            pass
        
        return results[:max_results]
    
    def download_file(self, url: str, local_path: str, 
                     chunk_size: int = 8192) -> Dict[str, Any]:
        """Download file from URL"""
        try:
            response = self.session.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            local_file = Path(local_path)
            local_file.parent.mkdir(parents=True, exist_ok=True)
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            with open(local_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
            
            return {
                'success': True,
                'url': url,
                'local_path': str(local_file),
                'file_size': downloaded,
                'content_type': response.headers.get('content-type'),
                'message': f'Downloaded {downloaded} bytes to {local_file}'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'url': url,
                'local_path': local_path
            }
    
    def check_url_status(self, urls: List[str]) -> Dict[str, Any]:
        """Check status of multiple URLs"""
        results = []
        
        for url in urls:
            try:
                response = self.session.head(url, timeout=10, allow_redirects=True)
                results.append({
                    'url': url,
                    'status_code': response.status_code,
                    'status': 'accessible' if response.status_code < 400 else 'error',
                    'content_type': response.headers.get('content-type'),
                    'content_length': response.headers.get('content-length'),
                    'final_url': response.url
                })
            except Exception as e:
                results.append({
                    'url': url,
                    'status': 'failed',
                    'error': str(e)
                })
        
        return {
            'success': True,
            'urls_checked': len(urls),
            'accessible': len([r for r in results if r.get('status') == 'accessible']),
            'failed': len([r for r in results if r.get('status') in ['failed', 'error']]),
            'results': results
        }