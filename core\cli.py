"""
Main CLI interface for AI Coding Assistant
"""

import click
import os
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from pathlib import Path

from core.config import Config
from core.session import Session
from llm.manager import LL<PERSON>anager
from ui.chat import ChatInterface
from ui.file_tree import FileTreeInterface
from tools.git_manager import GitManager
from utils.validation import validate_api_key

console = Console()

@click.group(invoke_without_command=True)
@click.option('--config', '-c', default='config.yaml', help='Configuration file path')
@click.option('--debug', '-d', is_flag=True, help='Enable debug mode')
@click.pass_context
def main(ctx, config, debug):
    """AI Coding Assistant - A powerful terminal-based AI coding assistant"""
    
    # Initialize configuration
    config_obj = Config(config)
    
    # Set debug mode
    if debug:
        config_obj.set_debug(True)
    
    # Store in context
    ctx.ensure_object(dict)
    ctx.obj['config'] = config_obj
    ctx.obj['debug'] = debug
    
    # If no subcommand is provided, start interactive mode
    if ctx.invoked_subcommand is None:
        start_interactive_mode(config_obj, debug)

def start_interactive_mode(config, debug):
    """Start the interactive chat mode"""
    console.print(Panel.fit(
        "[bold blue]AI Coding Assistant[/bold blue]\n"
        "Powerful terminal-based AI coding assistant with multi-LLM support",
        title="Welcome",
        border_style="blue"
    ))
    
    try:
        # Initialize LLM Manager
        llm_manager = LLMManager(config)
        
        # Initialize Session
        session = Session(config)
        
        # Initialize Git Manager
        git_manager = GitManager()
        
        # Start chat interface
        chat = ChatInterface(llm_manager, session, git_manager, config, debug)
        chat.start()
        
    except Exception as e:
        console.print(f"[red]Error initializing application: {e}[/red]")
        if debug:
            import traceback
            console.print(traceback.format_exc())

@main.command()
@click.option('--provider', '-p', default='gemini', help='LLM provider to test')
@click.option('--model', '-m', help='Specific model to test')
@click.pass_context
def test(ctx, provider, model):
    """Test API connection with specified provider"""
    config = ctx.obj['config']
    debug = ctx.obj['debug']
    
    console.print(f"[yellow]Testing {provider} API connection...[/yellow]")
    
    try:
        llm_manager = LLMManager(config)
        
        # Test the provider
        success = llm_manager.test_provider(provider, model)
        
        if success:
            console.print(f"[green]✓ {provider} API connection successful![/green]")
        else:
            console.print(f"[red]✗ {provider} API connection failed![/red]")
            
    except Exception as e:
        console.print(f"[red]Error testing API: {e}[/red]")
        if debug:
            import traceback
            console.print(traceback.format_exc())

@main.command()
@click.pass_context
def configure(ctx):
    """Configure API keys and settings"""
    config = ctx.obj['config']
    
    console.print("[yellow]Configuring AI Coding Assistant...[/yellow]")
    
    # Configure Gemini API Key
    gemini_key = click.prompt("Enter Gemini API Key", 
                             default=os.getenv("GEMINI_API_KEY", ""), 
                             hide_input=True, 
                             show_default=False)
    
    if gemini_key:
        config.set_api_key('gemini', gemini_key)
        console.print("[green]✓ Gemini API key configured[/green]")
    
    # Configure OpenAI API Key (optional)
    if click.confirm("Configure OpenAI API key?", default=False):
        openai_key = click.prompt("Enter OpenAI API Key", 
                                 default=os.getenv("OPENAI_API_KEY", ""), 
                                 hide_input=True, 
                                 show_default=False)
        if openai_key:
            config.set_api_key('openai', openai_key)
            console.print("[green]✓ OpenAI API key configured[/green]")
    
    # Configure Anthropic API Key (optional)
    if click.confirm("Configure Anthropic API key?", default=False):
        anthropic_key = click.prompt("Enter Anthropic API Key", 
                                   default=os.getenv("ANTHROPIC_API_KEY", ""), 
                                   hide_input=True, 
                                   show_default=False)
        if anthropic_key:
            config.set_api_key('anthropic', anthropic_key)
            console.print("[green]✓ Anthropic API key configured[/green]")
    
    # Save configuration
    config.save()
    console.print("[green]Configuration saved successfully![/green]")

@main.command()
@click.argument('file_path')
@click.option('--provider', '-p', default='gemini', help='LLM provider to use')
@click.option('--model', '-m', help='Specific model to use')
@click.pass_context
def analyze(ctx, file_path, provider, model):
    """Analyze a specific file"""
    config = ctx.obj['config']
    debug = ctx.obj['debug']
    
    if not os.path.exists(file_path):
        console.print(f"[red]File not found: {file_path}[/red]")
        return
    
    try:
        llm_manager = LLMManager(config)
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        prompt = f"Analyze this code file and provide insights, suggestions, and potential improvements:\n\n```\n{content}\n```"
        
        response = llm_manager.get_response(prompt, provider, model)
        
        console.print(Panel(
            response,
            title=f"Analysis of {file_path}",
            border_style="green"
        ))
        
    except Exception as e:
        console.print(f"[red]Error analyzing file: {e}[/red]")
        if debug:
            import traceback
            console.print(traceback.format_exc())

@main.command()
@click.pass_context
def status(ctx):
    """Show current status and configuration"""
    config = ctx.obj['config']
    
    console.print(Panel.fit(
        "[bold]AI Coding Assistant Status[/bold]",
        border_style="blue"
    ))
    
    # Show configured providers
    providers = config.get_configured_providers()
    console.print(f"[yellow]Configured Providers:[/yellow] {', '.join(providers)}")
    
    # Show default provider
    default_provider = config.get_default_provider()
    console.print(f"[yellow]Default Provider:[/yellow] {default_provider}")
    
    # Show current directory
    console.print(f"[yellow]Working Directory:[/yellow] {os.getcwd()}")
    
    # Show Git status if in a repo
    try:
        git_manager = GitManager()
        if git_manager.is_git_repo():
            branch = git_manager.get_current_branch()
            console.print(f"[yellow]Git Branch:[/yellow] {branch}")
            
            status = git_manager.get_status()
            if status:
                console.print(f"[yellow]Git Status:[/yellow] {len(status)} modified files")
    except:
        pass

if __name__ == "__main__":
    main()
