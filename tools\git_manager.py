"""
Git integration utilities for AI Coding Assistant
"""

import subprocess
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

class GitManager:
    """Git integration utilities"""
    
    def __init__(self, repo_path: str = "."):
        self.repo_path = Path(repo_path).resolve()
        self.git_dir = self.repo_path / ".git"
    
    def is_git_repo(self) -> bool:
        """Check if current directory is a git repository"""
        return self.git_dir.exists() and self.git_dir.is_dir()
    
    def _run_git_command(self, args: List[str], capture_output: bool = True) -> subprocess.CompletedProcess:
        """Run git command safely"""
        try:
            cmd = ["git"] + args
            result = subprocess.run(
                cmd,
                cwd=self.repo_path,
                capture_output=capture_output,
                text=True,
                check=False
            )
            return result
        except FileNotFoundError:
            raise Exception("Git is not installed or not in PATH")
    
    def get_status(self) -> List[Dict[str, str]]:
        """Get git status information"""
        if not self.is_git_repo():
            raise Exception("Not a git repository")
        
        result = self._run_git_command(["status", "--porcelain"])
        if result.returncode != 0:
            raise Exception(f"Git status failed: {result.stderr}")
        
        status_files = []
        for line in result.stdout.strip().split('\n'):
            if line:
                status_code = line[:2]
                file_path = line[3:]
                status_files.append({
                    'file': file_path,
                    'status': self._parse_status_code(status_code),
                    'status_code': status_code
                })
        
        return status_files
    
    def _parse_status_code(self, code: str) -> str:
        """Parse git status code to human readable format"""
        status_map = {
            'M ': 'Modified',
            ' M': 'Modified (unstaged)',
            'A ': 'Added',
            ' A': 'Added (unstaged)',
            'D ': 'Deleted',
            ' D': 'Deleted (unstaged)',
            'R ': 'Renamed',
            ' R': 'Renamed (unstaged)',
            'C ': 'Copied',
            ' C': 'Copied (unstaged)',
            'U ': 'Unmerged',
            ' U': 'Unmerged (unstaged)',
            '??': 'Untracked',
            '!!': 'Ignored'
        }
        return status_map.get(code, f'Unknown ({code})')
    
    def get_current_branch(self) -> str:
        """Get current git branch"""
        if not self.is_git_repo():
            raise Exception("Not a git repository")
        
        result = self._run_git_command(["branch", "--show-current"])
        if result.returncode != 0:
            raise Exception(f"Failed to get current branch: {result.stderr}")
        
        return result.stdout.strip()
    
    def get_branches(self) -> Dict[str, List[str]]:
        """Get all git branches"""
        if not self.is_git_repo():
            raise Exception("Not a git repository")
        
        # Local branches
        local_result = self._run_git_command(["branch"])
        local_branches = []
        current_branch = ""
        
        if local_result.returncode == 0:
            for line in local_result.stdout.strip().split('\n'):
                if line.startswith('*'):
                    current_branch = line[2:].strip()
                    local_branches.append(current_branch)
                elif line.strip():
                    local_branches.append(line.strip())
        
        # Remote branches
        remote_result = self._run_git_command(["branch", "-r"])
        remote_branches = []
        
        if remote_result.returncode == 0:
            for line in remote_result.stdout.strip().split('\n'):
                if line.strip() and not 'HEAD ->' in line:
                    remote_branches.append(line.strip())
        
        return {
            'local': local_branches,
            'remote': remote_branches,
            'current': [current_branch] if current_branch else []
        }
    
    def get_recent_commits(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent commit history"""
        if not self.is_git_repo():
            raise Exception("Not a git repository")
        
        result = self._run_git_command([
            "log", 
            f"--max-count={limit}",
            "--pretty=format:%H|%an|%ae|%ad|%s",
            "--date=iso"
        ])
        
        if result.returncode != 0:
            raise Exception(f"Failed to get commit history: {result.stderr}")
        
        commits = []
        for line in result.stdout.strip().split('\n'):
            if line:
                parts = line.split('|', 4)
                if len(parts) == 5:
                    commits.append({
                        'hash': parts[0],
                        'author_name': parts[1],
                        'author_email': parts[2],
                        'date': parts[3],
                        'message': parts[4]
                    })
        
        return commits
    
    def get_diff(self, file_path: Optional[str] = None, staged: bool = False) -> str:
        """Get diff for file or all changes"""
        if not self.is_git_repo():
            raise Exception("Not a git repository")
        
        args = ["diff"]
        if staged:
            args.append("--staged")
        if file_path:
            args.append(file_path)
        
        result = self._run_git_command(args)
        if result.returncode != 0:
            raise Exception(f"Failed to get diff: {result.stderr}")
        
        return result.stdout
    
    def add_files(self, file_paths: List[str]) -> bool:
        """Add files to git staging area"""
        if not self.is_git_repo():
            raise Exception("Not a git repository")
        
        result = self._run_git_command(["add"] + file_paths)
        return result.returncode == 0
    
    def commit(self, message: str, add_all: bool = False) -> Tuple[bool, str]:
        """Create a git commit"""
        if not self.is_git_repo():
            raise Exception("Not a git repository")
        
        args = ["commit", "-m", message]
        if add_all:
            args.insert(1, "-a")
        
        result = self._run_git_command(args)
        return result.returncode == 0, result.stdout + result.stderr
    
    def create_branch(self, branch_name: str, checkout: bool = True) -> bool:
        """Create a new git branch"""
        if not self.is_git_repo():
            raise Exception("Not a git repository")
        
        # Create branch
        result = self._run_git_command(["branch", branch_name])
        if result.returncode != 0:
            return False
        
        # Checkout if requested
        if checkout:
            checkout_result = self._run_git_command(["checkout", branch_name])
            return checkout_result.returncode == 0
        
        return True
    
    def checkout_branch(self, branch_name: str) -> bool:
        """Checkout a git branch"""
        if not self.is_git_repo():
            raise Exception("Not a git repository")
        
        result = self._run_git_command(["checkout", branch_name])
        return result.returncode == 0
    
    def get_file_history(self, file_path: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get commit history for specific file"""
        if not self.is_git_repo():
            raise Exception("Not a git repository")
        
        result = self._run_git_command([
            "log",
            f"--max-count={limit}",
            "--pretty=format:%H|%an|%ad|%s",
            "--date=iso",
            "--",
            file_path
        ])
        
        if result.returncode != 0:
            return []
        
        commits = []
        for line in result.stdout.strip().split('\n'):
            if line:
                parts = line.split('|', 3)
                if len(parts) == 4:
                    commits.append({
                        'hash': parts[0],
                        'author': parts[1],
                        'date': parts[2],
                        'message': parts[3]
                    })
        
        return commits
    
    def stash_changes(self, message: Optional[str] = None) -> bool:
        """Stash current changes"""
        if not self.is_git_repo():
            raise Exception("Not a git repository")
        
        args = ["stash"]
        if message:
            args.extend(["push", "-m", message])
        
        result = self._run_git_command(args)
        return result.returncode == 0
    
    def get_repo_info(self) -> Dict[str, Any]:
        """Get comprehensive repository information"""
        if not self.is_git_repo():
            raise Exception("Not a git repository")
        
        info = {}
        
        try:
            # Basic info
            info['current_branch'] = self.get_current_branch()
            info['status'] = self.get_status()
            info['branches'] = self.get_branches()
            
            # Remote info
            remote_result = self._run_git_command(["remote", "-v"])
            if remote_result.returncode == 0:
                remotes = {}
                for line in remote_result.stdout.strip().split('\n'):
                    if line:
                        parts = line.split()
                        if len(parts) >= 2:
                            name = parts[0]
                            url = parts[1]
                            if name not in remotes:
                                remotes[name] = url
                info['remotes'] = remotes
            
            # Recent commits
            info['recent_commits'] = self.get_recent_commits(5)
            
            # Uncommitted changes count
            status_files = self.get_status()
            info['uncommitted_changes'] = len(status_files)
            info['has_untracked'] = any(f['status_code'] == '??' for f in status_files)
            info['has_staged'] = any(f['status_code'][0] != ' ' and f['status_code'][0] != '?' for f in status_files)
            
        except Exception as e:
            info['error'] = str(e)
        
        return info
    
    def init_repo(self) -> bool:
        """Initialize a new git repository"""
        if self.is_git_repo():
            return True
        
        result = self._run_git_command(["init"])
        return result.returncode == 0
    
    def auto_commit_ai_changes(self, description: str, files: List[str]) -> Tuple[bool, str]:
        """Auto-commit AI-generated changes with descriptive message"""
        if not self.is_git_repo():
            return False, "Not a git repository"
        
        try:
            # Add specified files
            if files:
                add_result = self.add_files(files)
                if not add_result:
                    return False, "Failed to add files"
            
            # Create commit with AI description
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            commit_message = f"AI Assistant: {description}\n\nGenerated at {timestamp}"
            
            success, output = self.commit(commit_message)
            return success, output
            
        except Exception as e:
            return False, str(e)
