# AI Coding Assistant Configuration

# LLM Providers Configuration
providers:
  gemini:
    api_key: "AIzaSyDc7u7wTVdDG3zP18xnELKs0HX7-hImkmc"
    default_model: "gemini-2.0-flash"
    models:
      - "gemini-2.0-flash"
      - "gemini-2.5-flash"
      - "gemini-2.5-pro"
      - "gemini-1.5-flash"
      - "gemini-1.5-pro"
    timeout: 30
    max_tokens: 8192
    temperature: 0.7

  openai:
    api_key: ""  # Set via environment variable OPENAI_API_KEY or configure command
    default_model: "gpt-4o"
    models:
      - "gpt-4o"
      - "gpt-4o-mini"
      - "gpt-4-turbo"
      - "gpt-3.5-turbo"
    timeout: 30
    max_tokens: 4096
    temperature: 0.7

  anthropic:
    api_key: ""  # Set via environment variable ANTHROPIC_API_KEY or configure command
    default_model: "claude-sonnet-4-20250514"
    models:
      - "claude-sonnet-4-20250514"
      - "claude-3-7-sonnet-20250219"
      - "claude-3-5-sonnet-20241022"
      - "claude-3-sonnet-20240229"
      - "claude-3-haiku-20240307"
    timeout: 30
    max_tokens: 4096
    temperature: 0.7

# Default provider to use
default_provider: "gemini"

# Session Management
session:
  history_file: ".ai_assistant_history"
  max_history_size: 1000
  auto_save: true
  context_window: 10  # Number of previous messages to include in context
  max_context_files: 5  # Maximum number of files to keep in context

# Editor Configuration
editor:
  default_editor: "nano"  # Can be overridden by EDITOR environment variable
  syntax_highlighting: true
  line_numbers: true
  word_wrap: true
  theme: "monokai"
  tab_size: 4
  auto_indent: true

# Git Integration
git:
  auto_commit: false
  commit_message_template: "AI Assistant: {description}"
  auto_add: true  # Automatically add files before committing
  show_diff_before_commit: true
  max_diff_lines: 100
  ignore_patterns:
    - "*.log"
    - "*.tmp"
    - "*.temp"
    - "__pycache__"
    - "node_modules"
    - ".env"

# File Management
files:
  max_file_size: 10485760  # 10MB in bytes
  excluded_extensions:
    - ".exe"
    - ".bin"
    - ".so"
    - ".dll"
    - ".dylib"
    - ".img"
    - ".iso"
  excluded_directories:
    - "__pycache__"
    - "node_modules"
    - ".git"
    - ".vscode"
    - ".idea"
    - "build"
    - "dist"
    - ".mypy_cache"
    - ".pytest_cache"
  code_extensions:
    - ".py"
    - ".js"
    - ".ts"
    - ".jsx"
    - ".tsx"
    - ".java"
    - ".cpp"
    - ".c"
    - ".h"
    - ".cs"
    - ".php"
    - ".rb"
    - ".go"
    - ".rs"
    - ".swift"
    - ".kt"
    - ".scala"
    - ".html"
    - ".css"
    - ".scss"
    - ".sass"
    - ".less"
    - ".xml"
    - ".json"
    - ".yaml"
    - ".yml"
    - ".toml"
    - ".ini"
    - ".cfg"
    - ".conf"
    - ".md"
    - ".sql"
    - ".sh"
    - ".bash"
    - ".zsh"
    - ".fish"
    - ".ps1"
    - ".bat"

# Analysis Configuration
analysis:
  max_complexity_score: 20
  max_function_length: 50
  max_file_length: 1000
  check_todos: true
  check_style: true
  check_security: true
  severity_levels:
    - "error"
    - "warning"
    - "info"
    - "minor"

# UI Configuration
ui:
  color_theme: "dark"
  show_file_icons: true
  show_line_numbers: true
  max_tree_depth: 3
  max_file_list_items: 50
  truncate_long_lines: true
  max_line_length: 120
  show_hidden_files: false
  file_tree_icons: true

# Performance Settings
performance:
  max_concurrent_requests: 3
  cache_responses: true
  cache_size: 100
  request_timeout: 30
  retry_attempts: 3
  retry_delay: 1.0

# Security Settings
security:
  validate_file_paths: true
  restrict_to_project: true
  max_input_length: 10000
  sanitize_inputs: true
  allowed_file_operations:
    - "read"
    - "write"
    - "create"
    - "analyze"

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: ".ai_assistant.log"
  max_size: 10485760  # 10MB
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  log_api_calls: false  # Set to true for debugging
  log_file_operations: true

# Feature Flags
features:
  enable_git_integration: true
  enable_code_analysis: true
  enable_file_tree: true
  enable_syntax_highlighting: true
  enable_search: true
  enable_context_management: true
  enable_auto_backup: true
  enable_suggestions: true

# Keyboard Shortcuts (for future implementation)
shortcuts:
  quit: "Ctrl+C"
  clear: "Ctrl+L"
  help: "F1"
  save: "Ctrl+S"
  search: "Ctrl+F"

# Advanced Settings
advanced:
  debug_mode: false
  profile_performance: false
  experimental_features: false
  api_rate_limiting: true
  auto_update_check: false
