"""
Validation utilities for AI Coding Assistant
"""

import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
import subprocess

def validate_api_key(api_key: str, provider: str) -> Tuple[bool, str]:
    """Validate API key format and basic structure"""
    
    if not api_key or not api_key.strip():
        return False, "API key cannot be empty"
    
    api_key = api_key.strip()
    
    # Provider-specific validation
    if provider.lower() == 'openai':
        # OpenAI keys start with sk- and are typically 51 characters
        if not api_key.startswith('sk-'):
            return False, "OpenAI API key must start with 'sk-'"
        
        if len(api_key) < 40:
            return False, "OpenAI API key appears too short"
        
        # Check for valid characters (alphanumeric and some special chars)
        if not re.match(r'^sk-[A-Za-z0-9\-_]+$', api_key):
            return False, "OpenAI API key contains invalid characters"
    
    elif provider.lower() == 'anthropic':
        # Anthropic keys start with sk-ant- 
        if not api_key.startswith('sk-ant-'):
            return False, "Anthropic API key must start with 'sk-ant-'"
        
        if len(api_key) < 50:
            return False, "Anthropic API key appears too short"
        
        if not re.match(r'^sk-ant-[A-Za-z0-9\-_]+$', api_key):
            return False, "Anthropic API key contains invalid characters"
    
    elif provider.lower() == 'gemini':
        # Google/Gemini keys are typically 39 characters, alphanumeric
        if len(api_key) < 30:
            return False, "Gemini API key appears too short"
        
        if len(api_key) > 50:
            return False, "Gemini API key appears too long"
        
        # Gemini keys are typically alphanumeric with some special chars
        if not re.match(r'^[A-Za-z0-9\-_]+$', api_key):
            return False, "Gemini API key contains invalid characters"
    
    return True, "API key format appears valid"

def validate_file_path(file_path: str, must_exist: bool = True, 
                      must_be_file: bool = True, must_be_readable: bool = True) -> Tuple[bool, str]:
    """Validate file path and permissions"""
    
    if not file_path or not file_path.strip():
        return False, "File path cannot be empty"
    
    try:
        path = Path(file_path).resolve()
        
        # Check if path exists
        if must_exist and not path.exists():
            return False, f"Path does not exist: {file_path}"
        
        # Check if it's a file (when it exists)
        if path.exists() and must_be_file and not path.is_file():
            return False, f"Path is not a file: {file_path}"
        
        # Check if it's readable
        if path.exists() and must_be_readable and not os.access(path, os.R_OK):
            return False, f"File is not readable: {file_path}"
        
        # Security check - prevent access outside of reasonable bounds
        # This is a basic check and can be adjusted based on requirements
        cwd = Path.cwd().resolve()
        try:
            path.relative_to(cwd)
        except ValueError:
            # Path is outside current working directory
            # Allow if it's in user's home directory or common dev locations
            home = Path.home().resolve()
            try:
                path.relative_to(home)
            except ValueError:
                # Not in home directory either - this might be suspicious
                # but we'll allow it with a warning
                pass
        
        return True, "File path is valid"
        
    except Exception as e:
        return False, f"Invalid file path: {str(e)}"

def validate_directory_path(dir_path: str, must_exist: bool = True, 
                           must_be_writable: bool = False) -> Tuple[bool, str]:
    """Validate directory path and permissions"""
    
    if not dir_path or not dir_path.strip():
        return False, "Directory path cannot be empty"
    
    try:
        path = Path(dir_path).resolve()
        
        if must_exist and not path.exists():
            return False, f"Directory does not exist: {dir_path}"
        
        if path.exists() and not path.is_dir():
            return False, f"Path is not a directory: {dir_path}"
        
        if path.exists() and must_be_writable and not os.access(path, os.W_OK):
            return False, f"Directory is not writable: {dir_path}"
        
        return True, "Directory path is valid"
        
    except Exception as e:
        return False, f"Invalid directory path: {str(e)}"

def validate_git_repo(repo_path: str = ".") -> Tuple[bool, str]:
    """Validate if directory is a git repository"""
    
    try:
        path = Path(repo_path).resolve()
        
        if not path.exists():
            return False, f"Directory does not exist: {repo_path}"
        
        if not path.is_dir():
            return False, f"Path is not a directory: {repo_path}"
        
        git_dir = path / ".git"
        if not git_dir.exists():
            return False, "Not a git repository (no .git directory found)"
        
        # Try to run a simple git command to verify it's a valid repo
        try:
            result = subprocess.run(
                ["git", "rev-parse", "--git-dir"],
                cwd=path,
                capture_output=True,
                text=True,
                check=False
            )
            
            if result.returncode != 0:
                return False, "Invalid git repository"
            
        except FileNotFoundError:
            return False, "Git is not installed or not in PATH"
        
        return True, "Valid git repository"
        
    except Exception as e:
        return False, f"Error validating git repository: {str(e)}"

def validate_model_name(model_name: str, provider: str) -> Tuple[bool, str]:
    """Validate model name for given provider"""
    
    if not model_name or not model_name.strip():
        return False, "Model name cannot be empty"
    
    model_name = model_name.strip()
    
    # Define valid model patterns for each provider
    valid_models = {
        'openai': [
            r'^gpt-4o',
            r'^gpt-4o-mini',
            r'^gpt-4-turbo',
            r'^gpt-3\.5-turbo',
            r'^gpt-4$',
            r'^text-davinci-',
            r'^text-curie-',
            r'^text-babbage-',
            r'^text-ada-'
        ],
        'anthropic': [
            r'^claude-sonnet-4-\d{8}$',
            r'^claude-3-7-sonnet-\d{8}$',
            r'^claude-3-5-sonnet-\d{8}$',
            r'^claude-3-sonnet-\d{8}$',
            r'^claude-3-haiku-\d{8}$',
            r'^claude-3-opus-\d{8}$',
            r'^claude-2',
            r'^claude-instant'
        ],
        'gemini': [
            r'^gemini-2\.0-flash',
            r'^gemini-2\.5-flash',
            r'^gemini-2\.5-pro',
            r'^gemini-1\.5-flash',
            r'^gemini-1\.5-pro',
            r'^gemini-1\.0-pro',
            r'^text-bison',
            r'^chat-bison'
        ]
    }
    
    provider_patterns = valid_models.get(provider.lower(), [])
    
    if not provider_patterns:
        # If we don't have patterns for this provider, just do basic validation
        if not re.match(r'^[a-zA-Z0-9\-_.]+$', model_name):
            return False, "Model name contains invalid characters"
        return True, "Model name format appears valid"
    
    # Check against known patterns
    for pattern in provider_patterns:
        if re.match(pattern, model_name):
            return True, "Model name is valid"
    
    return False, f"Unknown model name for {provider}. Please verify the model name."

def validate_provider_name(provider: str) -> Tuple[bool, str]:
    """Validate LLM provider name"""
    
    if not provider or not provider.strip():
        return False, "Provider name cannot be empty"
    
    provider = provider.strip().lower()
    
    valid_providers = ['openai', 'anthropic', 'gemini', 'google']
    
    if provider not in valid_providers:
        return False, f"Unknown provider: {provider}. Valid providers: {', '.join(valid_providers)}"
    
    return True, "Provider name is valid"

def validate_commit_message(message: str) -> Tuple[bool, str]:
    """Validate git commit message"""
    
    if not message or not message.strip():
        return False, "Commit message cannot be empty"
    
    message = message.strip()
    
    # Check length (conventional recommendation is 50 chars for subject line)
    if len(message) > 200:
        return False, "Commit message is too long (max 200 characters)"
    
    # Check for minimum length
    if len(message) < 3:
        return False, "Commit message is too short (minimum 3 characters)"
    
    # Check for some basic patterns that might indicate problems
    if message.lower() in ['wip', 'temp', 'test', 'fix', 'update']:
        return False, "Commit message is too generic. Please be more descriptive."
    
    return True, "Commit message is valid"

def validate_branch_name(branch_name: str) -> Tuple[bool, str]:
    """Validate git branch name"""
    
    if not branch_name or not branch_name.strip():
        return False, "Branch name cannot be empty"
    
    branch_name = branch_name.strip()
    
    # Git branch name restrictions
    if branch_name.startswith('.') or branch_name.endswith('.'):
        return False, "Branch name cannot start or end with a dot"
    
    if branch_name.startswith('-') or branch_name.endswith('-'):
        return False, "Branch name cannot start or end with a hyphen"
    
    if '//' in branch_name:
        return False, "Branch name cannot contain consecutive slashes"
    
    if branch_name.endswith('/'):
        return False, "Branch name cannot end with a slash"
    
    # Check for invalid characters
    invalid_chars = [' ', '~', '^', ':', '?', '*', '[', '\\', '.lock', '@{']
    for char in invalid_chars:
        if char in branch_name:
            return False, f"Branch name cannot contain '{char}'"
    
    # Check for ASCII control characters
    if any(ord(c) < 32 or ord(c) == 127 for c in branch_name):
        return False, "Branch name cannot contain control characters"
    
    if len(branch_name) > 100:
        return False, "Branch name is too long (max 100 characters)"
    
    return True, "Branch name is valid"

def validate_file_content(content: str, max_size: int = 10_000_000) -> Tuple[bool, str]:
    """Validate file content for safety and size"""
    
    if content is None:
        return False, "Content cannot be None"
    
    # Check size
    content_size = len(content.encode('utf-8'))
    if content_size > max_size:
        size_mb = content_size / (1024 * 1024)
        max_mb = max_size / (1024 * 1024)
        return False, f"Content too large ({size_mb:.1f}MB, max {max_mb:.1f}MB)"
    
    # Check for binary content (presence of null bytes)
    if '\0' in content:
        return False, "Content appears to be binary (contains null bytes)"
    
    # Check for extremely long lines (might indicate binary or problematic content)
    lines = content.split('\n')
    max_line_length = 10000
    for i, line in enumerate(lines[:100]):  # Check first 100 lines
        if len(line) > max_line_length:
            return False, f"Line {i+1} is too long ({len(line)} chars, max {max_line_length})"
    
    return True, "Content is valid"

def validate_search_query(query: str) -> Tuple[bool, str]:
    """Validate search query"""
    
    if not query or not query.strip():
        return False, "Search query cannot be empty"
    
    query = query.strip()
    
    if len(query) < 2:
        return False, "Search query too short (minimum 2 characters)"
    
    if len(query) > 1000:
        return False, "Search query too long (maximum 1000 characters)"
    
    # Check for potentially dangerous patterns
    dangerous_patterns = [
        r'\.\./',  # Directory traversal
        r'[<>\"\'&;]',  # Potential injection characters
        r'\\x[0-9a-fA-F]{2}',  # Hex escape sequences
    ]
    
    for pattern in dangerous_patterns:
        if re.search(pattern, query):
            return False, "Search query contains potentially unsafe characters"
    
    return True, "Search query is valid"

def validate_config_value(key: str, value: Any, config_schema: Dict[str, Any]) -> Tuple[bool, str]:
    """Validate configuration value against schema"""
    
    if key not in config_schema:
        return False, f"Unknown configuration key: {key}"
    
    schema = config_schema[key]
    expected_type = schema.get('type')
    
    # Type validation
    if expected_type:
        if expected_type == 'string' and not isinstance(value, str):
            return False, f"Expected string for {key}, got {type(value).__name__}"
        elif expected_type == 'integer' and not isinstance(value, int):
            return False, f"Expected integer for {key}, got {type(value).__name__}"
        elif expected_type == 'boolean' and not isinstance(value, bool):
            return False, f"Expected boolean for {key}, got {type(value).__name__}"
        elif expected_type == 'list' and not isinstance(value, list):
            return False, f"Expected list for {key}, got {type(value).__name__}"
    
    # Range validation
    if 'min' in schema and isinstance(value, (int, float)) and value < schema['min']:
        return False, f"Value for {key} is below minimum ({schema['min']})"
    
    if 'max' in schema and isinstance(value, (int, float)) and value > schema['max']:
        return False, f"Value for {key} is above maximum ({schema['max']})"
    
    # Choice validation
    if 'choices' in schema and value not in schema['choices']:
        return False, f"Invalid choice for {key}. Valid options: {schema['choices']}"
    
    return True, "Configuration value is valid"

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safety"""
    
    # Remove or replace dangerous characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove control characters
    filename = ''.join(char for char in filename if ord(char) >= 32)
    
    # Remove leading/trailing whitespace and dots
    filename = filename.strip(' .')
    
    # Ensure not empty
    if not filename:
        filename = "untitled"
    
    # Prevent reserved names on Windows
    reserved_names = [
        'CON', 'PRN', 'AUX', 'NUL',
        'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
        'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    ]
    
    if filename.upper() in reserved_names:
        filename = f"_{filename}"
    
    # Limit length
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        max_name_length = 255 - len(ext)
        filename = name[:max_name_length] + ext
    
    return filename

def is_safe_path(path: str, base_path: str = ".") -> bool:
    """Check if path is safe (no directory traversal)"""
    try:
        base = Path(base_path).resolve()
        target = Path(path).resolve()
        
        # Check if target is within base directory
        target.relative_to(base)
        return True
        
    except ValueError:
        # Path is outside base directory
        return False
    except Exception:
        return False
