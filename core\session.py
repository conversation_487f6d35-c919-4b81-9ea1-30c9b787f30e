"""
Session management for AI Coding Assistant
"""

import json
import os
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

class Session:
    """Session manager for chat history and context"""
    
    def __init__(self, config):
        self.config = config
        self.session_config = config.get_session_config()
        self.history_file = Path(self.session_config.get('history_file', '.ai_assistant_history'))
        self.max_history_size = self.session_config.get('max_history_size', 1000)
        self.auto_save = self.session_config.get('auto_save', True)
        
        self.current_session = {
            'id': self._generate_session_id(),
            'start_time': datetime.now().isoformat(),
            'messages': [],
            'context': {
                'working_directory': str(Path.cwd()),
                'files_in_context': [],
                'provider': config.get_default_provider()
            }
        }
        
        self.history = self._load_history()
    
    def _generate_session_id(self) -> str:
        """Generate unique session ID"""
        return datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def _load_history(self) -> List[Dict[str, Any]]:
        """Load session history from file"""
        if not self.history_file.exists():
            return []
        
        try:
            with open(self.history_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading history: {e}")
            return []
    
    def _save_history(self) -> None:
        """Save session history to file"""
        if not self.auto_save:
            return
        
        try:
            # Limit history size
            if len(self.history) > self.max_history_size:
                self.history = self.history[-self.max_history_size:]
            
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.history, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving history: {e}")
    
    def add_message(self, role: str, content: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Add message to current session"""
        message = {
            'timestamp': datetime.now().isoformat(),
            'role': role,
            'content': content,
            'metadata': metadata or {}
        }
        
        self.current_session['messages'].append(message)
        
        if self.auto_save:
            self._save_current_session()
    
    def add_file_to_context(self, file_path: str) -> None:
        """Add file to current context"""
        abs_path = str(Path(file_path).resolve())
        if abs_path not in self.current_session['context']['files_in_context']:
            self.current_session['context']['files_in_context'].append(abs_path)
    
    def remove_file_from_context(self, file_path: str) -> None:
        """Remove file from current context"""
        abs_path = str(Path(file_path).resolve())
        if abs_path in self.current_session['context']['files_in_context']:
            self.current_session['context']['files_in_context'].remove(abs_path)
    
    def get_context_files(self) -> List[str]:
        """Get files in current context"""
        return self.current_session['context']['files_in_context'].copy()
    
    def clear_context(self) -> None:
        """Clear file context"""
        self.current_session['context']['files_in_context'] = []
    
    def get_conversation_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get conversation history for current session"""
        messages = self.current_session['messages']
        if limit:
            return messages[-limit:]
        return messages
    
    def set_provider(self, provider: str) -> None:
        """Set current provider"""
        self.current_session['context']['provider'] = provider
    
    def get_provider(self) -> str:
        """Get current provider"""
        return self.current_session['context']['provider']
    
    def _save_current_session(self) -> None:
        """Save current session to history"""
        # Update existing session or add new one
        session_found = False
        for i, session in enumerate(self.history):
            if session.get('id') == self.current_session['id']:
                self.history[i] = self.current_session.copy()
                session_found = True
                break
        
        if not session_found:
            self.history.append(self.current_session.copy())
        
        self._save_history()
    
    def end_session(self) -> None:
        """End current session"""
        self.current_session['end_time'] = datetime.now().isoformat()
        self._save_current_session()
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Get current session statistics"""
        messages = self.current_session['messages']
        user_messages = [m for m in messages if m['role'] == 'user']
        assistant_messages = [m for m in messages if m['role'] == 'assistant']
        
        return {
            'session_id': self.current_session['id'],
            'start_time': self.current_session['start_time'],
            'total_messages': len(messages),
            'user_messages': len(user_messages),
            'assistant_messages': len(assistant_messages),
            'files_in_context': len(self.get_context_files()),
            'current_provider': self.get_provider()
        }
    
    def search_history(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search through conversation history"""
        results = []
        query_lower = query.lower()
        
        for session in reversed(self.history):
            for message in reversed(session.get('messages', [])):
                if query_lower in message.get('content', '').lower():
                    results.append({
                        'session_id': session.get('id'),
                        'timestamp': message.get('timestamp'),
                        'role': message.get('role'),
                        'content': message.get('content'),
                        'preview': message.get('content')[:200] + '...' if len(message.get('content', '')) > 200 else message.get('content')
                    })
                    
                    if len(results) >= limit:
                        return results
        
        return results
