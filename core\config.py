"""
Configuration management for AI Coding Assistant
"""

import yaml
import os
from pathlib import Path
from typing import Dict, List, Any, Optional

class Config:
    """Configuration manager for the AI Coding Assistant"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = Path(config_path)
        self.config_data = self._load_config()
        self.debug = False
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default"""
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f) or {}
            except Exception as e:
                print(f"Error loading config: {e}")
                return self._get_default_config()
        else:
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'providers': {
                'gemini': {
                    'api_key': os.getenv('GEMINI_API_KEY', 'AIzaSyDc7u7wTVdDG3zP18xnELKs0HX7-hImkmc'),
                    'default_model': 'gemini-2.0-flash',
                    'models': [
                        'gemini-2.0-flash',
                        'gemini-2.5-flash',
                        'gemini-2.5-pro',
                        'gemini-1.5-flash',
                        'gemini-1.5-pro'
                    ]
                },
                'openai': {
                    'api_key': os.getenv('OPENAI_API_KEY', ''),
                    'default_model': 'gpt-4o',
                    'models': [
                        'gpt-4o',
                        'gpt-4o-mini',
                        'gpt-4-turbo',
                        'gpt-3.5-turbo'
                    ]
                },
                'anthropic': {
                    'api_key': os.getenv('ANTHROPIC_API_KEY', ''),
                    'default_model': 'claude-sonnet-4-20250514',
                    'models': [
                        'claude-sonnet-4-20250514',
                        'claude-3-7-sonnet-20250219',
                        'claude-3-5-sonnet-20241022',
                        'claude-3-sonnet-20240229'
                    ]
                }
            },
            'default_provider': 'gemini',
            'session': {
                'history_file': '.ai_assistant_history',
                'max_history_size': 1000,
                'auto_save': True
            },
            'editor': {
                'default_editor': os.getenv('EDITOR', 'nano'),
                'syntax_highlighting': True,
                'line_numbers': True
            },
            'git': {
                'auto_commit': False,
                'commit_message_template': 'AI Assistant: {description}'
            }
        }
    
    def save(self) -> None:
        """Save configuration to file"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config_data, f, default_flow_style=False, indent=2)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def get_api_key(self, provider: str) -> str:
        """Get API key for provider"""
        return self.config_data.get('providers', {}).get(provider, {}).get('api_key', '')
    
    def set_api_key(self, provider: str, api_key: str) -> None:
        """Set API key for provider"""
        if 'providers' not in self.config_data:
            self.config_data['providers'] = {}
        if provider not in self.config_data['providers']:
            self.config_data['providers'][provider] = {}
        self.config_data['providers'][provider]['api_key'] = api_key
    
    def get_default_model(self, provider: str) -> str:
        """Get default model for provider"""
        return self.config_data.get('providers', {}).get(provider, {}).get('default_model', '')
    
    def get_models(self, provider: str) -> List[str]:
        """Get available models for provider"""
        return self.config_data.get('providers', {}).get(provider, {}).get('models', [])
    
    def get_default_provider(self) -> str:
        """Get default provider"""
        return self.config_data.get('default_provider', 'gemini')
    
    def set_default_provider(self, provider: str) -> None:
        """Set default provider"""
        self.config_data['default_provider'] = provider
    
    def get_configured_providers(self) -> List[str]:
        """Get list of configured providers (with API keys)"""
        providers = []
        for provider, config in self.config_data.get('providers', {}).items():
            if config.get('api_key'):
                providers.append(provider)
        return providers
    
    def get_session_config(self) -> Dict[str, Any]:
        """Get session configuration"""
        return self.config_data.get('session', {})
    
    def get_editor_config(self) -> Dict[str, Any]:
        """Get editor configuration"""
        return self.config_data.get('editor', {})
    
    def get_git_config(self) -> Dict[str, Any]:
        """Get git configuration"""
        return self.config_data.get('git', {})
    
    def set_debug(self, debug: bool) -> None:
        """Set debug mode"""
        self.debug = debug
    
    def is_debug(self) -> bool:
        """Check if debug mode is enabled"""
        return self.debug
