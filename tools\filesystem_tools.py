"""
Advanced File System Tools for AI Coding Assistant
"""

import os
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import glob
import re
import json
from dataclasses import dataclass


@dataclass
class FileChange:
    """Represents a file change"""
    file_path: str
    change_type: str  # 'create', 'edit', 'delete'
    line_number: Optional[int] = None
    old_content: Optional[str] = None
    new_content: Optional[str] = None


class FilesystemTools:
    """Advanced filesystem operations with AI context awareness"""
    
    def __init__(self):
        self.recent_changes: List[FileChange] = []
        self.project_root = Path.cwd()
        
    def create_file(self, file_path: str, content: str = "", encoding: str = "utf-8") -> Dict[str, Any]:
        """Create a file with content"""
        try:
            path = Path(file_path)
            # Create parent directories if they don't exist
            path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(path, 'w', encoding=encoding) as f:
                f.write(content)
            
            change = FileChange(
                file_path=str(path),
                change_type='create',
                new_content=content
            )
            self.recent_changes.append(change)
            
            return {
                'success': True,
                'file_path': str(path),
                'size_bytes': len(content.encode(encoding)),
                'lines': content.count('\n') + 1 if content else 0,
                'message': f'File created: {path}'
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def edit_file(self, file_path: str, changes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Edit file with multiple operations (insert, delete, replace)"""
        try:
            path = Path(file_path)
            if not path.exists():
                return {'success': False, 'error': f'File not found: {file_path}'}
            
            with open(path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            original_content = ''.join(lines)
            
            # Sort changes by line number in reverse order to maintain line numbers
            changes_sorted = sorted(changes, key=lambda x: x.get('line', 0), reverse=True)
            
            for change in changes_sorted:
                operation = change.get('operation')
                line_num = change.get('line', 1) - 1  # Convert to 0-based
                
                if operation == 'insert':
                    content = change.get('content', '') + '\n'
                    lines.insert(line_num, content)
                    
                elif operation == 'delete':
                    if 0 <= line_num < len(lines):
                        lines.pop(line_num)
                        
                elif operation == 'replace':
                    if 0 <= line_num < len(lines):
                        lines[line_num] = change.get('content', '') + '\n'
                        
                elif operation == 'replace_range':
                    start = max(0, line_num)
                    end = min(len(lines), change.get('end_line', line_num + 1))
                    new_lines = [line + '\n' for line in change.get('content', '').split('\n')]
                    lines[start:end] = new_lines
            
            new_content = ''.join(lines)
            
            with open(path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            change_record = FileChange(
                file_path=str(path),
                change_type='edit',
                old_content=original_content,
                new_content=new_content
            )
            self.recent_changes.append(change_record)
            
            return {
                'success': True,
                'file_path': str(path),
                'operations_applied': len(changes),
                'lines_before': len(original_content.split('\n')),
                'lines_after': len(new_content.split('\n')),
                'message': f'File edited with {len(changes)} operations'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def create_directory(self, dir_path: str, recursive: bool = True) -> Dict[str, Any]:
        """Create directories"""
        try:
            path = Path(dir_path)
            if recursive:
                path.mkdir(parents=True, exist_ok=True)
            else:
                path.mkdir(exist_ok=True)
            
            return {
                'success': True,
                'directory': str(path),
                'exists': path.exists(),
                'message': f'Directory created: {path}'
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def read_file(self, file_path: str, line_start: Optional[int] = None, 
                  line_end: Optional[int] = None, encoding: str = "utf-8") -> Dict[str, Any]:
        """Read file content with optional line range"""
        try:
            path = Path(file_path)
            if not path.exists():
                return {'success': False, 'error': f'File not found: {file_path}'}
            
            with open(path, 'r', encoding=encoding) as f:
                if line_start is None and line_end is None:
                    content = f.read()
                    lines = content.split('\n')
                else:
                    lines = f.readlines()
                    start = max(0, (line_start or 1) - 1)
                    end = min(len(lines), line_end or len(lines))
                    selected_lines = lines[start:end]
                    content = ''.join(selected_lines)
                    lines = [line.rstrip('\n') for line in selected_lines]
            
            return {
                'success': True,
                'file_path': str(path),
                'content': content,
                'lines': lines,
                'total_lines': len(lines),
                'line_range': f'{line_start or 1}-{line_end or len(lines)}',
                'size_bytes': len(content.encode(encoding))
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def file_search(self, pattern: str, search_path: str = ".", recursive: bool = True) -> Dict[str, Any]:
        """Search files by glob patterns"""
        try:
            base_path = Path(search_path)
            if recursive:
                matches = list(base_path.rglob(pattern))
            else:
                matches = list(base_path.glob(pattern))
            
            files = []
            for match in matches:
                if match.is_file():
                    files.append({
                        'path': str(match),
                        'name': match.name,
                        'size': match.stat().st_size,
                        'modified': match.stat().st_mtime,
                        'relative_path': str(match.relative_to(base_path))
                    })
            
            return {
                'success': True,
                'pattern': pattern,
                'search_path': str(base_path),
                'files_found': len(files),
                'files': files
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def grep_search(self, pattern: str, search_path: str = ".", 
                   file_pattern: str = "*", regex: bool = False,
                   case_sensitive: bool = False) -> Dict[str, Any]:
        """Regex/text search inside files"""
        try:
            base_path = Path(search_path)
            matches = []
            
            # Get files matching the file pattern
            files = list(base_path.rglob(file_pattern))
            
            flags = 0 if case_sensitive else re.IGNORECASE
            if regex:
                search_regex = re.compile(pattern, flags)
            else:
                # Escape special regex characters for literal search
                escaped_pattern = re.escape(pattern)
                search_regex = re.compile(escaped_pattern, flags)
            
            for file_path in files:
                if file_path.is_file():
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            for line_num, line in enumerate(f, 1):
                                if search_regex.search(line):
                                    matches.append({
                                        'file': str(file_path),
                                        'line': line_num,
                                        'content': line.strip(),
                                        'match': search_regex.search(line).group()
                                    })
                    except Exception:
                        continue  # Skip files that can't be read
            
            return {
                'success': True,
                'pattern': pattern,
                'regex_mode': regex,
                'case_sensitive': case_sensitive,
                'files_searched': len(files),
                'matches_found': len(matches),
                'matches': matches
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def list_dir(self, directory: str = ".", show_hidden: bool = False,
                recursive: bool = False, max_depth: int = 3) -> Dict[str, Any]:
        """List files and directories"""
        try:
            path = Path(directory)
            if not path.exists():
                return {'success': False, 'error': f'Directory not found: {directory}'}
            
            items = []
            
            if recursive:
                for item in path.rglob("*"):
                    if not show_hidden and item.name.startswith('.'):
                        continue
                    
                    # Check depth
                    try:
                        relative = item.relative_to(path)
                        depth = len(relative.parts)
                        if depth > max_depth:
                            continue
                    except ValueError:
                        continue
                    
                    items.append(self._get_item_info(item, path))
            else:
                for item in path.iterdir():
                    if not show_hidden and item.name.startswith('.'):
                        continue
                    items.append(self._get_item_info(item, path))
            
            # Sort: directories first, then files
            items.sort(key=lambda x: (not x['is_directory'], x['name'].lower()))
            
            return {
                'success': True,
                'directory': str(path),
                'total_items': len(items),
                'directories': len([item for item in items if item['is_directory']]),
                'files': len([item for item in items if not item['is_directory']]),
                'items': items
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _get_item_info(self, item: Path, base_path: Path) -> Dict[str, Any]:
        """Get information about a file or directory"""
        try:
            stat = item.stat()
            return {
                'name': item.name,
                'path': str(item),
                'relative_path': str(item.relative_to(base_path)),
                'is_directory': item.is_dir(),
                'is_file': item.is_file(),
                'size': stat.st_size if item.is_file() else 0,
                'modified': stat.st_mtime,
                'permissions': oct(stat.st_mode)[-3:],
                'extension': item.suffix if item.is_file() else None
            }
        except Exception:
            return {
                'name': item.name,
                'path': str(item),
                'relative_path': str(item.relative_to(base_path)) if base_path else str(item),
                'is_directory': item.is_dir(),
                'is_file': item.is_file(),
                'size': 0,
                'modified': 0,
                'permissions': '000',
                'extension': item.suffix if item.is_file() else None
            }
    
    def get_recent_changes(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent file changes"""
        changes = []
        for change in self.recent_changes[-limit:]:
            changes.append({
                'file_path': change.file_path,
                'change_type': change.change_type,
                'line_number': change.line_number,
                'timestamp': 'recent'  # Could add actual timestamp
            })
        return changes
    
    def semantic_search(self, query: str, search_path: str = ".",
                       file_extensions: List[str] = None) -> Dict[str, Any]:
        """Natural language search across codebase"""
        try:
            if file_extensions is None:
                file_extensions = ['.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.cpp', '.c']
            
            base_path = Path(search_path)
            results = []
            
            # Simple semantic search based on keywords and context
            query_terms = query.lower().split()
            
            for ext in file_extensions:
                for file_path in base_path.rglob(f"*{ext}"):
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read().lower()
                            
                        # Calculate relevance score
                        score = 0
                        matches = []
                        
                        for term in query_terms:
                            if term in content:
                                score += content.count(term)
                                matches.append(term)
                        
                        if score > 0:
                            results.append({
                                'file': str(file_path),
                                'relative_path': str(file_path.relative_to(base_path)),
                                'score': score,
                                'matched_terms': matches,
                                'relevance': min(100, score * 10)  # Cap at 100
                            })
                    except Exception:
                        continue
            
            # Sort by relevance score
            results.sort(key=lambda x: x['score'], reverse=True)
            
            return {
                'success': True,
                'query': query,
                'files_searched': len(list(base_path.rglob("*"))),
                'results_found': len(results),
                'results': results[:20]  # Limit to top 20 results
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}