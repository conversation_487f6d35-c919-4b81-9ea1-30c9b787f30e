"""
Interactive chat interface for AI Coding Assistant
"""

import os
import sys
from typing import Dict, List, Any, Optional
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.markdown import Markdown
from rich.prompt import Prompt, Confirm
from rich.table import Table
from rich.live import Live
from rich.spinner import Spinner
from rich.layout import Layout
from rich.columns import Columns
from prompt_toolkit import prompt
from prompt_toolkit.history import InMemoryHistory
from prompt_toolkit.auto_suggest import AutoSuggestFromHistory
from prompt_toolkit.completion import WordCompleter

from ui.syntax_highlighter import SyntaxHighlighter
from ui.file_tree import FileTreeInterface
from tools.file_manager import FileManager
from tools.code_analyzer import CodeAnalyzer

class ChatInterface:
    """Interactive chat interface for the AI assistant"""
    
    def __init__(self, llm_manager, session, git_manager, config, debug=False):
        self.llm_manager = llm_manager
        self.session = session
        self.git_manager = git_manager
        self.config = config
        self.debug = debug
        
        self.console = Console()
        self.file_manager = FileManager()
        self.code_analyzer = CodeAnalyzer()
        self.syntax_highlighter = SyntaxHighlighter()
        self.file_tree = FileTreeInterface(self.file_manager)
        
        # Chat history
        self.history = InMemoryHistory()
        
        # Command completer
        self.commands = [
            '/help', '/quit', '/exit', '/clear', '/status', '/providers', '/models',
            '/switch', '/files', '/analyze', '/diff', '/commit', '/branch', '/stash',
            '/context', '/add', '/remove', '/save', '/load', '/search', '/tree'
        ]
        self.completer = WordCompleter(self.commands, ignore_case=True)
        
        # Current context
        self.current_provider = self.session.get_provider()
        self.current_model = None
        
    def start(self) -> None:
        """Start the interactive chat session"""
        self.console.print("[green]Welcome to AI Coding Assistant![/green]")
        self.console.print(f"[blue]Current provider:[/blue] {self.current_provider}")
        self.console.print("[yellow]Type /help for available commands[/yellow]")
        self.console.print()
        
        try:
            while True:
                try:
                    # Get user input
                    user_input = prompt(
                        "💬 ",
                        history=self.history,
                        auto_suggest=AutoSuggestFromHistory(),
                        completer=self.completer
                    ).strip()
                    
                    if not user_input:
                        continue
                    
                    # Handle commands
                    if user_input.startswith('/'):
                        if not self.handle_command(user_input):
                            break
                        continue
                    
                    # Process as chat message
                    self.process_chat_message(user_input)
                    
                except KeyboardInterrupt:
                    if Confirm.ask("\nDo you want to exit?"):
                        break
                    else:
                        self.console.print("[yellow]Continuing...[/yellow]")
                        continue
                        
                except EOFError:
                    break
                    
        except Exception as e:
            self.console.print(f"[red]Unexpected error: {e}[/red]")
            if self.debug:
                import traceback
                self.console.print(traceback.format_exc())
        
        finally:
            self.session.end_session()
            self.console.print("[green]Session ended. Goodbye![/green]")
    
    def handle_command(self, command: str) -> bool:
        """Handle chat commands. Returns False if should exit."""
        parts = command.split()
        cmd = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []
        
        if cmd in ['/quit', '/exit']:
            return False
        
        elif cmd == '/help':
            self.show_help()
        
        elif cmd == '/clear':
            os.system('cls' if os.name == 'nt' else 'clear')
        
        elif cmd == '/status':
            self.show_status()
        
        elif cmd == '/providers':
            self.show_providers()
        
        elif cmd == '/models':
            provider = args[0] if args else self.current_provider
            self.show_models(provider)
        
        elif cmd == '/switch':
            if len(args) >= 1:
                self.switch_provider(args[0], args[1] if len(args) > 1 else None)
            else:
                self.console.print("[red]Usage: /switch <provider> [model][/red]")
        
        elif cmd == '/files':
            self.show_files()
        
        elif cmd == '/analyze':
            if args:
                self.analyze_file(args[0])
            else:
                self.console.print("[red]Usage: /analyze <file_path>[/red]")
        
        elif cmd == '/diff':
            file_path = args[0] if args else None
            self.show_git_diff(file_path)
        
        elif cmd == '/commit':
            message = ' '.join(args) if args else None
            self.git_commit(message)
        
        elif cmd == '/branch':
            if args:
                if args[0] == 'list':
                    self.show_branches()
                elif args[0] == 'create':
                    if len(args) > 1:
                        self.create_branch(args[1])
                    else:
                        self.console.print("[red]Usage: /branch create <name>[/red]")
                elif args[0] == 'switch':
                    if len(args) > 1:
                        self.switch_branch(args[1])
                    else:
                        self.console.print("[red]Usage: /branch switch <name>[/red]")
            else:
                self.show_branches()
        
        elif cmd == '/stash':
            message = ' '.join(args) if args else None
            self.git_stash(message)
        
        elif cmd == '/context':
            self.show_context()
        
        elif cmd == '/add':
            if args:
                self.add_file_to_context(args[0])
            else:
                self.console.print("[red]Usage: /add <file_path>[/red]")
        
        elif cmd == '/remove':
            if args:
                self.remove_file_from_context(args[0])
            else:
                self.console.print("[red]Usage: /remove <file_path>[/red]")
        
        elif cmd == '/save':
            if len(args) >= 2:
                self.save_code_to_file(args[0], ' '.join(args[1:]))
            else:
                self.console.print("[red]Usage: /save <file_path> <content>[/red]")
        
        elif cmd == '/search':
            if args:
                query = ' '.join(args)
                self.search_history(query)
            else:
                self.console.print("[red]Usage: /search <query>[/red]")
        
        elif cmd == '/tree':
            self.show_file_tree()
        
        elif cmd == '/tools':
            self.show_tools()
        
        elif cmd == '/tool':
            if args:
                self.show_tool_help(args[0])
            else:
                self.show_tool_help()
        
        elif cmd == '/workflow':
            if args:
                workflow_request = ' '.join(args)
                self.execute_smart_workflow(workflow_request)
            else:
                self.console.print("[red]Usage: /workflow <description>[/red]")
        
        elif cmd == '/analytics':
            self.show_tool_analytics()
        
        else:
            self.console.print(f"[red]Unknown command: {cmd}[/red]")
            self.console.print("[yellow]Type /help for available commands[/yellow]")
        
        return True
    
    def process_chat_message(self, message: str) -> None:
        """Process user chat message"""
        # Add user message to session
        self.session.add_message('user', message)
        
        try:
            # Show thinking spinner
            with Live(Spinner('dots', text="Thinking..."), console=self.console, refresh_per_second=10):
                # Get conversation history for context
                history = self.session.get_conversation_history(10)
                
                # Include file context if available
                context_files = self.session.get_context_files()
                if context_files:
                    context_info = "\n\nFiles in context:\n"
                    for file_path in context_files[:3]:  # Limit to prevent token overflow
                        try:
                            content = self.file_manager.read_file(file_path)
                            context_info += f"\n--- {file_path} ---\n{content}\n"
                        except Exception as e:
                            context_info += f"\n--- {file_path} (Error: {e}) ---\n"
                    
                    message_with_context = message + context_info
                else:
                    message_with_context = message
                
                # Get response from LLM
                response = self.llm_manager.get_response(
                    message_with_context,
                    self.current_provider,
                    self.current_model,
                    messages=history
                )
            
            # Display response
            self.console.print()
            self.console.print(Panel(
                Markdown(response),
                title=f"🤖 {self.current_provider}",
                border_style="blue"
            ))
            self.console.print()
            
            # Add assistant response to session
            self.session.add_message('assistant', response)
            
        except Exception as e:
            self.console.print(f"[red]Error getting response: {e}[/red]")
            if self.debug:
                import traceback
                self.console.print(traceback.format_exc())
    
    def show_help(self) -> None:
        """Show help information"""
        help_text = """
[bold blue]AI Coding Assistant Commands[/bold blue]

[yellow]Chat Commands:[/yellow]
  /help                     - Show this help message
  /quit, /exit              - Exit the assistant
  /clear                    - Clear the screen
  /status                   - Show current status

[yellow]Provider & Model Commands:[/yellow]
  /providers                - List available providers
  /models [provider]        - List models for provider
  /switch <provider> [model] - Switch provider/model

[yellow]File Operations:[/yellow]
  /files                    - List files in project
  /tree                     - Show file tree
  /analyze <file>           - Analyze code file
  /save <file> <content>    - Save content to file

[yellow]Context Management:[/yellow]
  /context                  - Show current context
  /add <file>               - Add file to context
  /remove <file>            - Remove file from context

[yellow]Git Operations:[/yellow]
  /diff [file]              - Show git diff
  /commit [message]         - Create commit
  /branch [list|create|switch] - Git branch operations
  /stash [message]          - Stash changes

[yellow]History:[/yellow]
  /search <query>           - Search conversation history

[yellow]Tool Operations:[/yellow]
  /tools                    - List available tools
  /tool [name]              - Show tool help (all or specific)
  /workflow <description>   - Execute smart workflow
  /analytics                - Show tool usage analytics

[green]Just type your message to chat with the AI assistant![/green]
        """
        self.console.print(Panel(help_text, title="Help", border_style="cyan"))
    
    def show_status(self) -> None:
        """Show current status"""
        stats = self.session.get_session_stats()
        
        table = Table(title="Current Status")
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Provider", self.current_provider)
        table.add_row("Model", self.current_model or "Default")
        table.add_row("Session ID", stats['session_id'])
        table.add_row("Messages", str(stats['total_messages']))
        table.add_row("Files in Context", str(stats['files_in_context']))
        table.add_row("Working Directory", os.getcwd())
        
        # Git status
        try:
            if self.git_manager.is_git_repo():
                branch = self.git_manager.get_current_branch()
                status = self.git_manager.get_status()
                table.add_row("Git Branch", branch)
                table.add_row("Git Status", f"{len(status)} modified files")
        except:
            pass
        
        self.console.print(table)
    
    def show_providers(self) -> None:
        """Show available providers"""
        providers = self.llm_manager.get_available_providers()
        
        table = Table(title="Available Providers")
        table.add_column("Provider", style="cyan")
        table.add_column("Status", style="green")
        table.add_column("Default Model", style="yellow")
        
        for provider in providers:
            try:
                is_working = self.llm_manager.test_provider(provider)
                status = "✓ Working" if is_working else "✗ Error"
                default_model = self.llm_manager.get_default_model(provider)
                
                table.add_row(provider, status, default_model)
            except Exception as e:
                table.add_row(provider, f"✗ {str(e)}", "Unknown")
        
        self.console.print(table)
    
    def show_models(self, provider: str) -> None:
        """Show models for provider"""
        try:
            models = self.llm_manager.get_models(provider)
            if not models:
                self.console.print(f"[red]No models found for {provider}[/red]")
                return
            
            table = Table(title=f"Models for {provider}")
            table.add_column("Model", style="cyan")
            table.add_column("Status", style="green")
            
            default_model = self.llm_manager.get_default_model(provider)
            
            for model in models:
                status = "Default" if model == default_model else "Available"
                table.add_row(model, status)
            
            self.console.print(table)
            
        except Exception as e:
            self.console.print(f"[red]Error getting models: {e}[/red]")
    
    def switch_provider(self, provider: str, model: Optional[str] = None) -> None:
        """Switch LLM provider and optionally model"""
        try:
            available_providers = self.llm_manager.get_available_providers()
            if provider not in available_providers:
                self.console.print(f"[red]Provider {provider} not available[/red]")
                return
            
            # Test provider
            if not self.llm_manager.test_provider(provider):
                self.console.print(f"[red]Provider {provider} failed connection test[/red]")
                return
            
            self.current_provider = provider
            self.session.set_provider(provider)
            
            if model:
                available_models = self.llm_manager.get_models(provider)
                if model not in available_models:
                    self.console.print(f"[red]Model {model} not available for {provider}[/red]")
                    return
                self.current_model = model
            else:
                self.current_model = None
            
            self.console.print(f"[green]Switched to {provider}" + 
                             (f" with model {model}" if model else "") + "[/green]")
            
        except Exception as e:
            self.console.print(f"[red]Error switching provider: {e}[/red]")
    
    def show_files(self) -> None:
        """Show project files"""
        try:
            files = self.file_manager.get_code_files(20)
            
            table = Table(title="Project Files")
            table.add_column("File", style="cyan")
            table.add_column("Language", style="yellow")
            table.add_column("Size", style="green")
            
            for file_info in files:
                size_str = f"{file_info['size']} bytes"
                language = self.file_manager._get_language_from_extension(file_info['extension'])
                table.add_row(file_info['path'], language, size_str)
            
            self.console.print(table)
            
        except Exception as e:
            self.console.print(f"[red]Error listing files: {e}[/red]")
    
    def show_file_tree(self) -> None:
        """Show file tree"""
        try:
            tree_display = self.file_tree.render_tree()
            self.console.print(Panel(tree_display, title="File Tree", border_style="green"))
        except Exception as e:
            self.console.print(f"[red]Error showing file tree: {e}[/red]")
    
    def analyze_file(self, file_path: str) -> None:
        """Analyze a code file"""
        try:
            analysis = self.code_analyzer.analyze_file(file_path)
            
            self.console.print(Panel.fit(
                f"[bold]{analysis['language'].title()} Analysis[/bold]\n"
                f"Lines: {analysis['line_count']} | "
                f"Functions: {len(analysis['functions'])} | "
                f"Classes: {len(analysis['classes'])} | "
                f"Complexity: {analysis['complexity_score']}",
                title=f"Analysis: {file_path}",
                border_style="blue"
            ))
            
            # Show issues if any
            if analysis['issues']:
                self.console.print("\n[yellow]Issues Found:[/yellow]")
                for issue in analysis['issues'][:10]:  # Limit to 10 issues
                    severity_colors = {
                        'error': 'red',
                        'warning': 'yellow',
                        'info': 'blue',
                        'minor': 'dim'
                    }
                    color = severity_colors.get(issue['severity'], 'white')
                    self.console.print(f"  [{color}]Line {issue['line']}: {issue['message']}[/{color}]")
            
            # Show functions
            if analysis['functions']:
                self.console.print("\n[green]Functions:[/green]")
                for func in analysis['functions'][:10]:  # Limit to 10 functions
                    args_str = f"({', '.join(func.get('args', []))})"
                    self.console.print(f"  Line {func['line']}: {func['name']}{args_str}")
                    
        except Exception as e:
            self.console.print(f"[red]Error analyzing file: {e}[/red]")
    
    def show_git_diff(self, file_path: Optional[str] = None) -> None:
        """Show git diff"""
        try:
            if not self.git_manager.is_git_repo():
                self.console.print("[red]Not a git repository[/red]")
                return
            
            diff = self.git_manager.get_diff(file_path)
            if not diff:
                self.console.print("[green]No changes to show[/green]")
                return
            
            # Highlight diff
            highlighted_diff = self.syntax_highlighter.highlight_diff(diff)
            self.console.print(Panel(
                highlighted_diff,
                title=f"Git Diff{' - ' + file_path if file_path else ''}",
                border_style="yellow"
            ))
            
        except Exception as e:
            self.console.print(f"[red]Error getting diff: {e}[/red]")
    
    def git_commit(self, message: Optional[str] = None) -> None:
        """Create git commit"""
        try:
            if not self.git_manager.is_git_repo():
                self.console.print("[red]Not a git repository[/red]")
                return
            
            status = self.git_manager.get_status()
            if not status:
                self.console.print("[green]No changes to commit[/green]")
                return
            
            if not message:
                message = Prompt.ask("Commit message")
            
            success, output = self.git_manager.commit(message, add_all=True)
            if success:
                self.console.print(f"[green]Commit successful![/green]")
                self.console.print(output)
            else:
                self.console.print(f"[red]Commit failed: {output}[/red]")
                
        except Exception as e:
            self.console.print(f"[red]Error committing: {e}[/red]")
    
    def show_branches(self) -> None:
        """Show git branches"""
        try:
            if not self.git_manager.is_git_repo():
                self.console.print("[red]Not a git repository[/red]")
                return
            
            branches = self.git_manager.get_branches()
            
            table = Table(title="Git Branches")
            table.add_column("Branch", style="cyan")
            table.add_column("Type", style="yellow")
            table.add_column("Status", style="green")
            
            current = branches.get('current', '')
            
            for branch in branches.get('local', []):
                status = "Current" if branch == current else "Local"
                table.add_row(branch, "Local", status)
            
            for branch in branches.get('remote', [])[:10]:  # Limit remote branches
                table.add_row(branch, "Remote", "Remote")
            
            self.console.print(table)
            
        except Exception as e:
            self.console.print(f"[red]Error getting branches: {e}[/red]")
    
    def create_branch(self, branch_name: str) -> None:
        """Create new git branch"""
        try:
            if not self.git_manager.is_git_repo():
                self.console.print("[red]Not a git repository[/red]")
                return
            
            success = self.git_manager.create_branch(branch_name)
            if success:
                self.console.print(f"[green]Created and switched to branch '{branch_name}'[/green]")
            else:
                self.console.print(f"[red]Failed to create branch '{branch_name}'[/red]")
                
        except Exception as e:
            self.console.print(f"[red]Error creating branch: {e}[/red]")
    
    def switch_branch(self, branch_name: str) -> None:
        """Switch git branch"""
        try:
            if not self.git_manager.is_git_repo():
                self.console.print("[red]Not a git repository[/red]")
                return
            
            success = self.git_manager.checkout_branch(branch_name)
            if success:
                self.console.print(f"[green]Switched to branch '{branch_name}'[/green]")
            else:
                self.console.print(f"[red]Failed to switch to branch '{branch_name}'[/red]")
                
        except Exception as e:
            self.console.print(f"[red]Error switching branch: {e}[/red]")
    
    def git_stash(self, message: Optional[str] = None) -> None:
        """Stash changes"""
        try:
            if not self.git_manager.is_git_repo():
                self.console.print("[red]Not a git repository[/red]")
                return
            
            success = self.git_manager.stash_changes(message)
            if success:
                self.console.print("[green]Changes stashed successfully[/green]")
            else:
                self.console.print("[red]Failed to stash changes[/red]")
                
        except Exception as e:
            self.console.print(f"[red]Error stashing: {e}[/red]")
    
    def show_context(self) -> None:
        """Show current file context"""
        context_files = self.session.get_context_files()
        
        if not context_files:
            self.console.print("[yellow]No files in context[/yellow]")
            return
        
        table = Table(title="Files in Context")
        table.add_column("File", style="cyan")
        table.add_column("Size", style="green")
        
        for file_path in context_files:
            try:
                file_info = self.file_manager.get_file_info(file_path)
                size_str = f"{file_info['size']} bytes"
                table.add_row(file_path, size_str)
            except:
                table.add_row(file_path, "Error")
        
        self.console.print(table)
    
    def add_file_to_context(self, file_path: str) -> None:
        """Add file to context"""
        try:
            if not os.path.exists(file_path):
                self.console.print(f"[red]File not found: {file_path}[/red]")
                return
            
            self.session.add_file_to_context(file_path)
            self.console.print(f"[green]Added {file_path} to context[/green]")
            
        except Exception as e:
            self.console.print(f"[red]Error adding file to context: {e}[/red]")
    
    def remove_file_from_context(self, file_path: str) -> None:
        """Remove file from context"""
        try:
            self.session.remove_file_from_context(file_path)
            self.console.print(f"[green]Removed {file_path} from context[/green]")
            
        except Exception as e:
            self.console.print(f"[red]Error removing file from context: {e}[/red]")
    
    def save_code_to_file(self, file_path: str, content: str) -> None:
        """Save code content to file"""
        try:
            self.file_manager.write_file(file_path, content)
            self.console.print(f"[green]Saved content to {file_path}[/green]")
            
        except Exception as e:
            self.console.print(f"[red]Error saving file: {e}[/red]")
    
    def search_history(self, query: str) -> None:
        """Search conversation history"""
        try:
            results = self.session.search_history(query, limit=10)
            
            if not results:
                self.console.print("[yellow]No results found[/yellow]")
                return
            
            self.console.print(f"[green]Found {len(results)} results:[/green]\n")
            
            for i, result in enumerate(results, 1):
                role_color = "blue" if result['role'] == 'user' else "green"
                self.console.print(f"[{role_color}]{i}. {result['role'].title()}:[/{role_color}] {result['preview']}")
                self.console.print(f"   [dim]{result['timestamp']} - Session {result['session_id']}[/dim]\n")
                
        except Exception as e:
            self.console.print(f"[red]Error searching history: {e}[/red]")
    
    def show_file_tree(self) -> None:
        """Show project file tree"""
        try:
            from ui.file_tree import FileTreeViewer
            tree_viewer = FileTreeViewer()
            tree = tree_viewer.generate_tree()
            self.console.print(tree)
            
        except Exception as e:
            self.console.print(f"[red]Error in file tree: {e}[/red]")
    
    # Tool Management Methods
    
    def show_tools(self) -> None:
        """Show available tools"""
        try:
            tools = self.llm_manager.get_available_tools()
            
            if not tools:
                self.console.print("[yellow]No tools available[/yellow]")
                return
            
            # Group tools by category
            tool_categories = {
                'filesystem': [],
                'testing': [],
                'terminal': [],
                'web': [],
                'ai_reasoning': [],
                'orchestration': []
            }
            
            for tool in tools:
                # Categorize based on prefix or common patterns
                if any(prefix in tool for prefix in ['create_file', 'read_file', 'edit_file', 'file_search', 'grep_search', 'list_dir', 'semantic_search']):
                    tool_categories['filesystem'].append(tool)
                elif any(prefix in tool for prefix in ['test_', 'run_tests', 'autonomous_debugger', 'lint_check']):
                    tool_categories['testing'].append(tool)
                elif any(prefix in tool for prefix in ['run_in_terminal', 'get_terminal', 'install_python', 'configure_python']):
                    tool_categories['terminal'].append(tool)
                elif any(prefix in tool for prefix in ['fetch_webpage', 'semantic_web_search', 'github_repo']):
                    tool_categories['web'].append(tool)
                elif any(prefix in tool for prefix in ['natural_language', 'intent_recognition', 'chain_of_thought', 'predict_next']):
                    tool_categories['ai_reasoning'].append(tool)
                elif any(prefix in tool for prefix in ['execute_smart_workflow', 'parallel_tool']):
                    tool_categories['orchestration'].append(tool)
                else:
                    tool_categories['filesystem'].append(tool)  # Default category
            
            table = Table(title="Available Tools")
            table.add_column("Category", style="cyan", width=15)
            table.add_column("Tools", style="green")
            
            for category, category_tools in tool_categories.items():
                if category_tools:
                    tools_text = ', '.join(category_tools)
                    table.add_row(category.title(), tools_text)
            
            self.console.print(table)
            self.console.print(f"\n[blue]Total: {len(tools)} tools available[/blue]")
            self.console.print("[yellow]Use '/tool <name>' for detailed help on a specific tool[/yellow]")
            
        except Exception as e:
            self.console.print(f"[red]Error showing tools: {e}[/red]")
    
    def show_tool_help(self, tool_name: str = None) -> None:
        """Show help for specific tool or all tools"""
        try:
            help_result = self.llm_manager.get_tool_help(tool_name)
            
            if not help_result.get('success'):
                self.console.print(f"[red]Error: {help_result.get('error', 'Unknown error')}[/red]")
                return
            
            if tool_name:
                # Show help for specific tool
                definition = help_result.get('definition', {})
                
                panel_content = f"""
[bold blue]{definition.get('name', tool_name)}[/bold blue]

[yellow]Description:[/yellow]
{definition.get('description', 'No description available')}

[yellow]Category:[/yellow] {definition.get('category', 'unknown')}

[yellow]Parameters:[/yellow]
"""
                
                parameters = definition.get('parameters', {})
                if parameters:
                    for param_name, param_info in parameters.items():
                        required = " (required)" if param_info.get('required') else " (optional)"
                        param_type = param_info.get('type', 'unknown')
                        param_desc = param_info.get('description', 'No description')
                        default = f" [default: {param_info.get('default')}]" if 'default' in param_info else ""
                        
                        panel_content += f"\n  • {param_name} ({param_type}){required}: {param_desc}{default}"
                else:
                    panel_content += "\n  No parameters"
                
                usage_examples = definition.get('usage_examples', [])
                if usage_examples:
                    panel_content += f"\n\n[yellow]Usage Examples:[/yellow]"
                    for example in usage_examples:
                        panel_content += f"\n  {example}"
                
                panel_content += f"\n\n[yellow]Returns:[/yellow] {definition.get('returns', 'No return information')}"
                
                self.console.print(Panel(panel_content, title=f"Tool Help: {tool_name}", border_style="blue"))
            else:
                # Show summary of all tools
                tools_data = help_result.get('tools', {})
                
                table = Table(title="All Tools Summary")
                table.add_column("Tool Name", style="cyan")
                table.add_column("Category", style="yellow")
                table.add_column("Description", style="green")
                
                for tool_name, tool_info in tools_data.items():
                    description = tool_info.get('description', 'No description')
                    # Truncate long descriptions
                    if len(description) > 60:
                        description = description[:57] + "..."
                    
                    table.add_row(
                        tool_name,
                        tool_info.get('category', 'unknown'),
                        description
                    )
                
                self.console.print(table)
                self.console.print(f"\n[blue]Total: {help_result.get('total_tools', 0)} tools[/blue]")
                self.console.print("[yellow]Use '/tool <name>' for detailed help on a specific tool[/yellow]")
                
        except Exception as e:
            self.console.print(f"[red]Error showing tool help: {e}[/red]")
    
    def execute_smart_workflow(self, workflow_request: str) -> None:
        """Execute smart workflow"""
        try:
            self.console.print(f"[yellow]Executing smart workflow: {workflow_request}[/yellow]")
            
            # Show progress spinner
            with Live(Spinner('dots', text="Executing workflow..."), console=self.console, refresh_per_second=10):
                result = self.llm_manager.execute_smart_workflow(workflow_request)
            
            if result.get('success'):
                workflow_steps = result.get('workflow_steps', [])
                final_report = result.get('final_report', {})
                
                # Show workflow summary
                table = Table(title="Workflow Execution Summary")
                table.add_column("Step", style="cyan")
                table.add_column("Result", style="green")
                table.add_column("Duration", style="yellow")
                
                for i, step in enumerate(workflow_steps, 1):
                    step_name = step.get('step', f'Step {i}')
                    step_result = step.get('result', {})
                    step_success = "✓ Success" if step_result.get('success') else "✗ Failed"
                    step_duration = f"{step.get('duration', 0):.2f}s"
                    
                    table.add_row(step_name, step_success, step_duration)
                
                self.console.print(table)
                
                # Show final report
                if final_report:
                    report_text = f"""
[bold]Total Steps:[/bold] {final_report.get('total_steps', 0)}
[bold]Successful:[/bold] {final_report.get('successful_steps', 0)}
[bold]Failed:[/bold] {final_report.get('failed_steps', 0)}
[bold]Success Rate:[/bold] {final_report.get('success_rate', 0):.1f}%
[bold]Total Duration:[/bold] {final_report.get('total_duration', 0):.2f}s
[bold]Efficiency:[/bold] {final_report.get('workflow_efficiency', 0):.2f}
                    """
                    
                    recommendations = final_report.get('recommendations', [])
                    if recommendations:
                        report_text += f"\n\n[bold]Recommendations:[/bold]\n"
                        for rec in recommendations:
                            report_text += f"• {rec}\n"
                    
                    self.console.print(Panel(report_text.strip(), title="Workflow Report", border_style="green"))
                
            else:
                error_msg = result.get('error', 'Unknown workflow error')
                self.console.print(f"[red]Workflow failed: {error_msg}[/red]")
                
        except Exception as e:
            self.console.print(f"[red]Error executing workflow: {e}[/red]")
    
    def show_tool_analytics(self) -> None:
        """Show tool usage analytics"""
        try:
            analytics = self.llm_manager.get_tool_analytics()
            
            if not analytics.get('success'):
                self.console.print(f"[red]Error: {analytics.get('error', 'Unknown error')}[/red]")
                return
            
            if 'message' in analytics:
                self.console.print(f"[yellow]{analytics['message']}[/yellow]")
                return
            
            data = analytics.get('analytics', {})
            
            # Main statistics table
            table = Table(title="Tool Usage Analytics")
            table.add_column("Metric", style="cyan")
            table.add_column("Value", style="green")
            
            table.add_row("Total Executions", str(data.get('total_executions', 0)))
            table.add_row("Successful Executions", str(data.get('successful_executions', 0)))
            table.add_row("Success Rate", f"{data.get('success_rate', 0):.1f}%")
            table.add_row("Recent Success Rate", f"{data.get('recent_success_rate', 0):.1f}%")
            table.add_row("Average Duration", f"{data.get('average_duration', 0):.3f}s")
            table.add_row("Total Duration", f"{data.get('total_duration', 0):.3f}s")
            table.add_row("Available Tools", str(data.get('available_tools', 0)))
            table.add_row("Tool Categories", str(data.get('tool_categories', 0)))
            
            self.console.print(table)
            
            # Most used tools
            most_used = data.get('most_used_tools', [])
            if most_used:
                usage_table = Table(title="Most Used Tools")
                usage_table.add_column("Tool", style="cyan")
                usage_table.add_column("Usage Count", style="green")
                
                for tool_name, count in most_used:
                    usage_table.add_row(tool_name, str(count))
                
                self.console.print(usage_table)
            
        except Exception as e:
            self.console.print(f"[red]Error showing analytics: {e}[/red]")
