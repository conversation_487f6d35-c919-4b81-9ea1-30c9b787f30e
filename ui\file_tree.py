"""
File tree interface for AI Coding Assistant
"""

import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from rich.tree import Tree
from rich.text import Text
from rich.console import Console

class FileTreeInterface:
    """File tree interface for navigating project structure"""
    
    def __init__(self, file_manager):
        self.file_manager = file_manager
        self.console = Console()
        
        # File type icons (using simple text symbols for terminal compatibility)
        self.file_icons = {
            '.py': '🐍',
            '.js': '📜',
            '.ts': '📘',
            '.jsx': '⚛️',
            '.tsx': '⚛️',
            '.html': '🌐',
            '.css': '🎨',
            '.scss': '🎨',
            '.sass': '🎨',
            '.json': '📋',
            '.yaml': '📄',
            '.yml': '📄',
            '.md': '📝',
            '.txt': '📄',
            '.xml': '📄',
            '.sql': '🗃️',
            '.sh': '⚡',
            '.bash': '⚡',
            '.bat': '⚡',
            '.ps1': '⚡',
            '.java': '☕',
            '.cpp': '⚙️',
            '.c': '⚙️',
            '.h': '⚙️',
            '.cs': '🔷',
            '.php': '🐘',
            '.rb': '💎',
            '.go': '🐹',
            '.rs': '🦀',
            '.swift': '🍎',
            '.kt': '🅺',
            '.scala': '⚖️',
            '.r': '📊',
            '.m': '🍎',
            '.vue': '💚',
            '.svelte': '🧡',
            '.dart': '🎯',
            '.dockerfile': '🐳',
            '.gitignore': '🙈',
            '.env': '⚙️',
            '.toml': '📄',
            '.ini': '⚙️',
            '.cfg': '⚙️',
            '.conf': '⚙️'
        }
        
        # Directory icons
        self.dir_icons = {
            '.git': '📁',
            'node_modules': '📦',
            '__pycache__': '🗂️',
            '.vscode': '💻',
            '.idea': '💡',
            'build': '🔨',
            'dist': '📦',
            'target': '🎯',
            'bin': '⚙️',
            'lib': '📚',
            'src': '📁',
            'test': '🧪',
            'tests': '🧪',
            'docs': '📖',
            'assets': '🖼️',
            'images': '🖼️',
            'static': '📄',
            'public': '🌐',
            'scripts': '📜',
            'tools': '🔧',
            'utils': '🛠️',
            'config': '⚙️',
            'settings': '⚙️'
        }
    
    def render_tree(self, max_depth: int = 3, show_hidden: bool = False) -> Tree:
        """Render file tree as Rich Tree object"""
        tree_data = self.file_manager.get_file_tree(max_depth, show_hidden)
        
        # Create root tree
        root_name = tree_data.get('name', 'Project')
        tree = Tree(f"📁 {root_name}")
        
        # Build tree recursively
        self._build_tree_node(tree, tree_data.get('children', []))
        
        return tree
    
    def _build_tree_node(self, parent_node: Tree, children: List[Dict[str, Any]]) -> None:
        """Build tree node recursively"""
        for child in children:
            name = child['name']
            file_type = child['type']
            child_children = child.get('children', [])
            
            if file_type == 'directory':
                # Directory
                icon = self.dir_icons.get(name.lower(), '📁')
                node_text = f"{icon} {name}"
                
                # Add size info for directories with children
                if child_children:
                    node_text += f" ({len(child_children)} items)"
                
                dir_node = parent_node.add(node_text, style="bold blue")
                
                # Recursively add children
                if child_children:
                    self._build_tree_node(dir_node, child_children)
            else:
                # File
                file_ext = Path(name).suffix.lower()
                icon = self.file_icons.get(file_ext, '📄')
                size = child.get('size', 0)
                size_str = self._format_file_size(size)
                
                node_text = f"{icon} {name}"
                if size > 0:
                    node_text += f" ({size_str})"
                
                # Color code by file type
                style = self._get_file_style(file_ext)
                parent_node.add(node_text, style=style)
    
    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB"]
        size = float(size_bytes)
        i = 0
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        if i == 0:
            return f"{int(size)}{size_names[i]}"
        else:
            return f"{size:.1f}{size_names[i]}"
    
    def _get_file_style(self, file_ext: str) -> str:
        """Get Rich style for file based on extension"""
        code_extensions = {
            '.py': 'green',
            '.js': 'yellow',
            '.ts': 'blue',
            '.jsx': 'cyan',
            '.tsx': 'cyan',
            '.html': 'red',
            '.css': 'magenta',
            '.scss': 'magenta',
            '.sass': 'magenta',
            '.json': 'blue',
            '.yaml': 'blue',
            '.yml': 'blue',
            '.md': 'white',
            '.java': 'red',
            '.cpp': 'blue',
            '.c': 'blue',
            '.cs': 'green',
            '.php': 'magenta',
            '.rb': 'red',
            '.go': 'cyan',
            '.rs': 'red',
            '.swift': 'red',
            '.kt': 'magenta'
        }
        
        return code_extensions.get(file_ext, 'white')
    
    def get_file_list(self, directory: str = ".", pattern: str = "*", 
                     file_type: str = "all", max_results: int = 50) -> List[Dict[str, Any]]:
        """Get formatted file list"""
        try:
            files = self.file_manager.find_files(pattern, file_type, max_results)
            
            # Add display formatting
            for file_info in files:
                file_path = file_info['path']
                file_ext = Path(file_path).suffix.lower()
                
                file_info['icon'] = self.file_icons.get(file_ext, '📄')
                file_info['size_formatted'] = self._format_file_size(file_info['size'])
                file_info['style'] = self._get_file_style(file_ext)
            
            return files
            
        except Exception as e:
            self.console.print(f"[red]Error getting file list: {e}[/red]")
            return []
    
    def navigate_to_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Navigate to and get info about a specific file"""
        try:
            return self.file_manager.get_file_info(file_path)
        except Exception as e:
            self.console.print(f"[red]Error navigating to file: {e}[/red]")
            return None
    
    def get_directory_summary(self, directory: str = ".") -> Dict[str, Any]:
        """Get summary of directory contents"""
        try:
            structure = self.file_manager.get_project_structure()
            
            # Add visual formatting
            summary = {
                'total_files': structure['total_files'],
                'total_directories': structure['total_directories'],
                'code_files': structure['code_files'],
                'languages': structure['languages'],
                'file_types': structure['file_types'],
                'largest_files': [],
                'recently_modified': []
            }
            
            # Format largest files
            for file_info in structure['largest_files'][:5]:
                file_info['size_formatted'] = self._format_file_size(file_info['size'])
                file_info['icon'] = self.file_icons.get(
                    Path(file_info['path']).suffix.lower(), '📄'
                )
                summary['largest_files'].append(file_info)
            
            # Format recently modified files
            for file_info in structure['recently_modified'][:5]:
                file_info['size_formatted'] = self._format_file_size(file_info['size'])
                file_info['icon'] = self.file_icons.get(
                    Path(file_info['path']).suffix.lower(), '📄'
                )
                summary['recently_modified'].append(file_info)
            
            return summary
            
        except Exception as e:
            self.console.print(f"[red]Error getting directory summary: {e}[/red]")
            return {}
    
    def filter_files_by_type(self, file_type: str) -> List[Dict[str, Any]]:
        """Filter files by programming language or file type"""
        try:
            if file_type.lower() == 'code':
                return self.file_manager.get_code_files(50)
            
            # Map language names to extensions
            language_extensions = {
                'python': ['.py'],
                'javascript': ['.js', '.jsx'],
                'typescript': ['.ts', '.tsx'],
                'html': ['.html', '.htm'],
                'css': ['.css', '.scss', '.sass', '.less'],
                'java': ['.java'],
                'cpp': ['.cpp', '.cxx', '.cc'],
                'c': ['.c'],
                'csharp': ['.cs'],
                'php': ['.php'],
                'ruby': ['.rb'],
                'go': ['.go'],
                'rust': ['.rs'],
                'swift': ['.swift'],
                'kotlin': ['.kt'],
                'scala': ['.scala'],
                'json': ['.json'],
                'yaml': ['.yaml', '.yml'],
                'markdown': ['.md', '.markdown'],
                'xml': ['.xml'],
                'sql': ['.sql'],
                'shell': ['.sh', '.bash', '.zsh', '.fish'],
                'powershell': ['.ps1'],
                'batch': ['.bat', '.cmd']
            }
            
            extensions = language_extensions.get(file_type.lower(), [])
            if not extensions:
                return []
            
            # Find files with matching extensions
            all_files = []
            for ext in extensions:
                pattern = f"*{ext}"
                files = self.file_manager.find_files(pattern, "files", 50)
                all_files.extend(files)
            
            # Remove duplicates and add formatting
            seen = set()
            unique_files = []
            for file_info in all_files:
                if file_info['path'] not in seen:
                    seen.add(file_info['path'])
                    
                    file_ext = Path(file_info['path']).suffix.lower()
                    file_info['icon'] = self.file_icons.get(file_ext, '📄')
                    file_info['size_formatted'] = self._format_file_size(file_info['size'])
                    file_info['style'] = self._get_file_style(file_ext)
                    
                    unique_files.append(file_info)
            
            return unique_files[:50]  # Limit results
            
        except Exception as e:
            self.console.print(f"[red]Error filtering files: {e}[/red]")
            return []
    
    def search_files(self, query: str, search_content: bool = False) -> List[Dict[str, Any]]:
        """Search for files by name or content"""
        try:
            results = []
            
            if search_content:
                # Search file contents (limited to text files)
                code_files = self.file_manager.get_code_files(100)
                
                for file_info in code_files:
                    try:
                        content = self.file_manager.read_file(file_info['path'])
                        if query.lower() in content.lower():
                            file_info['match_type'] = 'content'
                            file_info['icon'] = self.file_icons.get(
                                Path(file_info['path']).suffix.lower(), '📄'
                            )
                            file_info['size_formatted'] = self._format_file_size(file_info['size'])
                            results.append(file_info)
                    except:
                        continue
            else:
                # Search file names
                pattern = f"*{query}*"
                files = self.file_manager.find_files(pattern, "all", 50)
                
                for file_info in files:
                    file_info['match_type'] = 'filename'
                    file_ext = Path(file_info['path']).suffix.lower()
                    file_info['icon'] = self.file_icons.get(file_ext, '📄')
                    file_info['size_formatted'] = self._format_file_size(file_info['size'])
                    results.append(file_info)
            
            return results
            
        except Exception as e:
            self.console.print(f"[red]Error searching files: {e}[/red]")
            return []
    
    def get_tree_stats(self) -> Dict[str, Any]:
        """Get statistics about the file tree"""
        try:
            structure = self.file_manager.get_project_structure()
            
            return {
                'total_files': structure['total_files'],
                'total_directories': structure['total_directories'],
                'code_files': structure['code_files'],
                'code_percentage': round((structure['code_files'] / max(structure['total_files'], 1)) * 100, 1),
                'top_languages': dict(sorted(structure['languages'].items(), 
                                           key=lambda x: x[1], reverse=True)[:5]),
                'top_file_types': dict(sorted(structure['file_types'].items(), 
                                            key=lambda x: x[1], reverse=True)[:5])
            }
            
        except Exception as e:
            self.console.print(f"[red]Error getting tree stats: {e}[/red]")
            return {}
