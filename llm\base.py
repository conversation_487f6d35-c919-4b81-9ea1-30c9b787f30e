"""
Base classes for LLM providers
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional

class LL<PERSON>rovider(ABC):
    """Abstract base class for LLM providers"""
    
    def __init__(self, api_key: str, config: Dict[str, Any]):
        self.api_key = api_key
        self.config = config
        self.client = None
        self._initialize_client()
    
    @abstractmethod
    def _initialize_client(self) -> None:
        """Initialize the LLM client"""
        pass
    
    @abstractmethod
    def get_response(self, prompt: str, model: Optional[str] = None, **kwargs) -> str:
        """Get response from the LLM"""
        pass
    
    @abstractmethod
    def get_available_models(self) -> List[str]:
        """Get list of available models"""
        pass
    
    @abstractmethod
    def test_connection(self) -> bool:
        """Test API connection"""
        pass
    
    def get_default_model(self) -> str:
        """Get default model for this provider"""
        return self.config.get('default_model', '')
    
    def format_conversation(self, messages: List[Dict[str, str]]) -> Any:
        """Format conversation history for the provider"""
        return messages
    
    def get_token_count(self, text: str) -> int:
        """Estimate token count (basic implementation)"""
        # Simple estimation: ~4 characters per token
        return len(text) // 4
    
    def supports_system_message(self) -> bool:
        """Check if provider supports system messages"""
        return True
    
    def supports_function_calling(self) -> bool:
        """Check if provider supports function calling"""
        return False
    
    def get_max_tokens(self, model: Optional[str] = None) -> int:
        """Get maximum token limit for model"""
        return 4096  # Default fallback
