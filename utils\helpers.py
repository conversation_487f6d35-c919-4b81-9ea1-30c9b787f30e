"""
Helper utilities for AI Coding Assistant
"""

import os
import re
import sys
import shlex
from datetime import datetime, timezone
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
import unicodedata

def format_timestamp(timestamp: Union[datetime, float, str], format_type: str = "relative") -> str:
    """Format timestamp in various formats"""
    
    # Convert to datetime if needed
    if isinstance(timestamp, (int, float)):
        dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
    elif isinstance(timestamp, str):
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        except ValueError:
            return timestamp  # Return as-is if can't parse
    else:
        dt = timestamp
    
    now = datetime.now(tz=timezone.utc)
    
    if format_type == "relative":
        # Relative time formatting
        diff = now - dt
        
        if diff.days > 365:
            years = diff.days // 365
            return f"{years} year{'s' if years > 1 else ''} ago"
        elif diff.days > 30:
            months = diff.days // 30
            return f"{months} month{'s' if months > 1 else ''} ago"
        elif diff.days > 0:
            return f"{diff.days} day{'s' if diff.days > 1 else ''} ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hour{'s' if hours > 1 else ''} ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minute{'s' if minutes > 1 else ''} ago"
        else:
            return "Just now"
    
    elif format_type == "short":
        return dt.strftime("%Y-%m-%d %H:%M")
    
    elif format_type == "long":
        return dt.strftime("%Y-%m-%d %H:%M:%S %Z")
    
    elif format_type == "iso":
        return dt.isoformat()
    
    else:
        return dt.strftime(format_type)

def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate text to specified length with suffix"""
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def safe_filename(filename: str, max_length: int = 255) -> str:
    """Convert string to safe filename"""
    # Remove or replace invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove control characters
    filename = ''.join(char for char in filename if unicodedata.category(char)[0] != 'C')
    
    # Normalize unicode
    filename = unicodedata.normalize('NFKD', filename)
    
    # Remove leading/trailing whitespace and dots
    filename = filename.strip(' .')
    
    # Ensure not empty
    if not filename:
        filename = "untitled"
    
    # Truncate if too long
    if len(filename) > max_length:
        name, ext = os.path.splitext(filename)
        max_name_length = max_length - len(ext)
        filename = name[:max_name_length] + ext
    
    return filename

def parse_command_args(command_string: str) -> List[str]:
    """Parse command string into arguments, handling quotes properly"""
    try:
        return shlex.split(command_string)
    except ValueError:
        # Fallback to simple split if shlex fails
        return command_string.split()

def extract_code_blocks(text: str) -> List[Dict[str, str]]:
    """Extract code blocks from markdown-style text"""
    code_blocks = []
    
    # Pattern for fenced code blocks
    pattern = r'```(\w+)?\n(.*?)\n```'
    matches = re.finditer(pattern, text, re.DOTALL)
    
    for match in matches:
        language = match.group(1) or 'text'
        code = match.group(2)
        
        code_blocks.append({
            'language': language,
            'code': code,
            'start': match.start(),
            'end': match.end()
        })
    
    return code_blocks

def get_file_encoding(file_path: str) -> str:
    """Detect file encoding"""
    try:
        import chardet
        
        with open(file_path, 'rb') as f:
            raw_data = f.read(10000)  # Read first 10KB
            result = chardet.detect(raw_data)
            return result['encoding'] or 'utf-8'
    except ImportError:
        # Fallback if chardet not available
        return 'utf-8'
    except Exception:
        return 'utf-8'

def is_binary_file(file_path: str) -> bool:
    """Check if file is binary"""
    try:
        with open(file_path, 'rb') as f:
            chunk = f.read(1024)
            return b'\0' in chunk
    except Exception:
        return True

def get_line_count(file_path: str) -> int:
    """Get number of lines in file"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            return sum(1 for _ in f)
    except Exception:
        return 0

def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    size = float(size_bytes)
    i = 0
    
    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1
    
    if i == 0:
        return f"{int(size)} {size_names[i]}"
    else:
        return f"{size:.1f} {size_names[i]}"

def sanitize_input(text: str) -> str:
    """Sanitize user input for security"""
    # Remove null bytes
    text = text.replace('\0', '')
    
    # Limit length
    if len(text) > 10000:
        text = text[:10000]
    
    # Remove potentially dangerous characters for shell commands
    dangerous_chars = ['`', '$', ';', '&', '|', '>', '<', '(', ')', '{', '}']
    for char in dangerous_chars:
        text = text.replace(char, '')
    
    return text.strip()

def extract_file_extension(filename: str) -> str:
    """Extract file extension from filename"""
    return Path(filename).suffix.lower()

def get_relative_path(file_path: str, base_path: str = ".") -> str:
    """Get relative path from base path"""
    try:
        return str(Path(file_path).relative_to(Path(base_path)))
    except ValueError:
        return file_path

def create_backup_filename(file_path: str) -> str:
    """Create backup filename with timestamp"""
    path = Path(file_path)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return str(path.with_suffix(f'.{timestamp}{path.suffix}.backup'))

def merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """Merge two dictionaries recursively"""
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = merge_dicts(result[key], value)
        else:
            result[key] = value
    
    return result

def flatten_dict(d: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
    """Flatten nested dictionary"""
    items = []
    
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    
    return dict(items)

def chunk_text(text: str, chunk_size: int = 1000, overlap: int = 100) -> List[str]:
    """Split text into overlapping chunks"""
    if len(text) <= chunk_size:
        return [text]
    
    chunks = []
    start = 0
    
    while start < len(text):
        end = start + chunk_size
        
        # Try to break at word boundary
        if end < len(text):
            # Look for last space within the chunk
            last_space = text.rfind(' ', start, end)
            if last_space > start:
                end = last_space
        
        chunks.append(text[start:end])
        start = end - overlap
        
        if start >= len(text):
            break
    
    return chunks

def get_system_info() -> Dict[str, Any]:
    """Get system information"""
    import platform
    
    return {
        'platform': platform.system(),
        'platform_version': platform.version(),
        'architecture': platform.architecture()[0],
        'python_version': platform.python_version(),
        'working_directory': os.getcwd(),
        'user': os.getenv('USER', os.getenv('USERNAME', 'unknown')),
        'home': os.path.expanduser('~')
    }

def normalize_line_endings(text: str, style: str = 'unix') -> str:
    """Normalize line endings in text"""
    # First normalize to Unix style
    text = text.replace('\r\n', '\n').replace('\r', '\n')
    
    if style == 'windows':
        return text.replace('\n', '\r\n')
    elif style == 'mac':
        return text.replace('\n', '\r')
    else:  # unix
        return text

def escape_ansi(text: str) -> str:
    """Remove ANSI escape sequences from text"""
    ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
    return ansi_escape.sub('', text)

def calculate_similarity(text1: str, text2: str) -> float:
    """Calculate similarity between two texts (simple Jaccard similarity)"""
    if not text1 and not text2:
        return 1.0
    if not text1 or not text2:
        return 0.0
    
    # Convert to sets of words
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())
    
    # Calculate Jaccard similarity
    intersection = len(words1.intersection(words2))
    union = len(words1.union(words2))
    
    return intersection / union if union > 0 else 0.0

def debounce(wait_time: float):
    """Decorator to debounce function calls"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            def call_func():
                return func(*args, **kwargs)
            
            # Simple debouncing - in a real implementation you'd want
            # to use threading.Timer for proper debouncing
            return call_func()
        
        return wrapper
    return decorator

def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """Decorator to retry function on failure"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        import time
                        time.sleep(delay * (attempt + 1))
                    continue
            
            if last_exception:
                raise last_exception
            else:
                raise Exception("All retry attempts failed")
        
        return wrapper
    return decorator

def memoize(func):
    """Simple memoization decorator"""
    cache = {}
    
    def wrapper(*args, **kwargs):
        # Create a cache key from args and kwargs
        key = str(args) + str(sorted(kwargs.items()))
        
        if key not in cache:
            cache[key] = func(*args, **kwargs)
        
        return cache[key]
    
    return wrapper
